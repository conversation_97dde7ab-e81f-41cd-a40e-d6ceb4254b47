document.addEventListener("DOMContentLoaded", () => {
    const form = document.getElementById("add-form")
    const titleInput = document.getElementById("title")
    const urlInput = document.getElementById("url")
    const groupSelect = document.getElementById("group")
    const newGroupBtn = document.getElementById("new-group-btn")
    const imagePreview = document.getElementById("image-preview")
    const statusDiv = document.getElementById("status")
  
    let pageIcon = null
  
    // 添加数据库选择器
    const dbSelectorHtml = `
      <div class="form-group db-selector">
        <label for="db-select">选择库</label>
        <select id="db-select" name="db">
          <!-- 数据库选项将通过AJAX加载 -->
        </select>
      </div>
    `
  
    // 在表单开头插入数据库选择器
    form.insertAdjacentHTML("afterbegin", dbSelectorHtml)
    const dbSelect = document.getElementById("db-select")
  
    // 添加粘贴图片按钮
    const previewContainer = document.querySelector(".image-preview")
    const pasteImageBtn = document.createElement("button")
    pasteImageBtn.type = "button"
    pasteImageBtn.id = "paste-image-btn"
    pasteImageBtn.className = "paste-btn"
    pasteImageBtn.textContent = "粘贴图片"
    previewContainer.insertAdjacentElement("afterend", pasteImageBtn)
  
    // 添加粘贴图片按钮样式
    const style = document.createElement("style")
    style.textContent = `
      .paste-btn {
        margin-top: 8px;
        padding: 5px 10px;
        background-color: #f0f0f0;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        display: block;
        width: 100%;
      }
      
      .paste-btn:hover {
        background-color: #e0e0e0;
      }
      
      .db-selector {
        margin-bottom: 12px;
      }
    `
    document.head.appendChild(style)
  
    // 加载数据库列表
    loadDatabases()
  
    // 获取当前标签页信息
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const tab = tabs[0]
      titleInput.value = tab.title || ""
      urlInput.value = tab.url || ""
  
      // 获取网站图标
      if (tab.favIconUrl) {
        imagePreview.src = tab.favIconUrl
        pageIcon = tab.favIconUrl
      }
  
      // 如果是从右键菜单打开的，获取传递的数据
      chrome.storage.local.get(["contextData"], (result) => {
        if (result.contextData) {
          if (result.contextData.title) {
            titleInput.value = result.contextData.title
          }
          if (result.contextData.url) {
            urlInput.value = result.contextData.url
            // 检查URL是否已存在
            checkUrlExists(result.contextData.url)
          }
          if (result.contextData.img) {
            imagePreview.src = result.contextData.img
            pageIcon = result.contextData.img
          }
          if (result.contextData.note) {
            document.getElementById("note").value = result.contextData.note
          }
  
          // 清除存储的数据
          chrome.storage.local.remove(["contextData"])
        } else {
          // 如果不是从右键菜单打开的，检查URL是否已存在
          checkUrlExists(tab.url)
        }
      })
    })
  
    // 检查URL是否已存在
    function checkUrlExists(url) {
      if (!url) return
  
      const currentDb = dbSelect.value
  
      fetch(`http://localhost:4157/api/check-url-exists?url=${encodeURIComponent(url)}&db=${currentDb}`)
        .then((response) => response.json())
        .then((data) => {
          if (data.exists) {
            // URL已存在，填充表单
            titleInput.value = data.website.title
            groupSelect.value = data.group
            document.getElementById("note").value = data.website.note || ""
            if (data.website.img) {
              imagePreview.src = `http://localhost:4157/static/${data.website.img}`
              pageIcon = `http://localhost:4157/static/${data.website.img}`
            }
  
            showStatus("此网址已存在，已加载保存的信息", "info")
          }
        })
        .catch((error) => {
          console.error("检查URL出错:", error)
        })
    }
  
    // 加载数据库列表
    function loadDatabases() {
      fetch("http://localhost:4157/api/databases")
        .then((response) => response.json())
        .then((databases) => {
          dbSelect.innerHTML = ""
  
          // 获取上次选择的数据库
          chrome.storage.local.get(["lastSelectedDb"], (result) => {
            const lastDb = result.lastSelectedDb || "data"
  
            databases.forEach((db) => {
              const option = document.createElement("option")
              option.value = db
              option.textContent = db
              option.selected = db === lastDb
              dbSelect.appendChild(option)
            })
  
            // 加载分组
            loadGroups(lastDb)
  
            // 数据库变更事件
            dbSelect.addEventListener("change", () => {
              loadGroups(dbSelect.value)
              // 保存选择
              chrome.storage.local.set({ lastSelectedDb: dbSelect.value })
              // 重新检查URL是否存在
              checkUrlExists(urlInput.value)
            })
          })
        })
        .catch((error) => {
          console.error("加载数据库出错:", error)
          showStatus("无法连接到导航网站，请确保网站已启动", "error")
        })
    }
  
    // 加载分组列表
    function loadGroups(db = "data") {
      fetch(`http://localhost:4157/api/groups?db=${db}`)
        .then((response) => response.json())
        .then((groups) => {
          groupSelect.innerHTML = ""
  
          if (groups.length === 0) {
            // 如果没有分组，添加默认分组
            const option = document.createElement("option")
            option.value = "工具"
            option.textContent = "工具"
            groupSelect.appendChild(option)
          } else {
            groups.forEach((group) => {
              const option = document.createElement("option")
              option.value = group
              option.textContent = group
              groupSelect.appendChild(option)
            })
          }
        })
        .catch((error) => {
          console.error("加载分组出错:", error)
          showStatus("无法连接到导航网站，请确保网站已启动", "error")
        })
    }
  
    // 新建分组按钮点击事件
    newGroupBtn.addEventListener("click", () => {
      const groupName = prompt("请输入新分组名称:")
      if (!groupName || groupName.trim() === "") return
  
      const currentDb = dbSelect.value
  
      fetch("http://localhost:4157/api/add-group", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: groupName.trim() }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // 添加新分组到下拉列表
            const option = document.createElement("option")
            option.value = groupName.trim()
            option.textContent = groupName.trim()
            groupSelect.appendChild(option)
            groupSelect.value = groupName.trim()
          } else {
            showStatus(data.error || "添加分组失败", "error")
          }
        })
        .catch((error) => {
          console.error("添加分组出错:", error)
          showStatus("添加分组时出错", "error")
        })
    })
  
    // 粘贴图片按钮点击事件
    pasteImageBtn.addEventListener("click", () => {
      navigator.clipboard
        .read()
        .then((items) => {
          for (const item of items) {
            if (item.types.includes("image/png") || item.types.includes("image/jpeg")) {
              item.getType("image/png").then((blob) => {
                const reader = new FileReader()
                reader.onload = (e) => {
                  imagePreview.src = e.target.result
                  pageIcon = e.target.result
                }
                reader.readAsDataURL(blob)
              })
              return
            }
          }
          alert("剪贴板中没有图片")
        })
        .catch((err) => {
          console.error("无法读取剪贴板内容:", err)
          alert("无法读取剪贴板内容，请确保已授予权限")
        })
    })
  
    // 表单提交事件
    form.addEventListener("submit", (e) => {
      e.preventDefault()
  
      const title = titleInput.value.trim()
      const url = urlInput.value.trim()
      const group = groupSelect.value
      const note = document.getElementById("note").value.trim()
      const db = dbSelect.value
  
      if (!title || !url || !group) {
        showStatus("请填写必填字段", "error")
        return
      }
  
      // 禁用提交按钮
      document.getElementById("submit-btn").disabled = true
  
      // 准备要发送的数据
      const data = {
        title: title,
        url: url,
        group: group,
        note: note,
        img: pageIcon,
        db: db,
      }
  
      // 发送到服务器
      fetch("http://localhost:4157/api/extension/add", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })
        .then((response) => response.json())
        .then((result) => {
          if (result.success) {
            showStatus("添加成功！", "success")
            setTimeout(() => {
              window.close()
            }, 1500)
          } else {
            showStatus(result.error || "添加失败", "error")
            document.getElementById("submit-btn").disabled = false
          }
        })
        .catch((error) => {
          console.error("添加网站出错:", error)
          showStatus("无法连接到导航网站，请确保网站已启动", "error")
          document.getElementById("submit-btn").disabled = false
        })
    })
  
    // 显示状态信息
    function showStatus(message, type) {
      statusDiv.textContent = message
      statusDiv.className = "status " + type
  
      if (type === "success") {
        setTimeout(() => {
          statusDiv.className = "status"
        }, 3000)
      }
    }
  })
  
  