#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索功能测试脚本
"""

import json

def load_data(db_name='data'):
    """加载数据"""
    try:
        with open(f'{db_name}.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def search_websites(query, db_name='data'):
    """搜索网站"""
    query = query.lower().strip()
    
    if not query:
        return []
        
    data = load_data(db_name)
    results = []

    for group, websites in data.items():
        for website in websites:
            # 确保必要的字段存在且不为None
            title = (website.get('title') or '').lower()
            url = (website.get('url') or '').lower()
            note = (website.get('note') or '').lower()
            
            # 搜索title和url字段
            if query in title or query in url or query in note:
                website_copy = website.copy()
                website_copy['group'] = group
                results.append(website_copy)

    return results

def test_search():
    """测试搜索功能"""
    print("=== 搜索功能测试 ===\n")
    
    # 测试用例
    test_cases = [
        "抠图",      # 应该找到"抠图"网站
        "百度",      # 应该找到"百度翻译"
        "ai",        # 应该找到多个AI相关网站
        "github",    # 应该找到GitHub相关网站
        "不存在的关键词"  # 应该返回空结果
    ]
    
    for query in test_cases:
        print(f"搜索关键词: '{query}'")
        results = search_websites(query)
        print(f"找到 {len(results)} 个结果:")
        
        for i, result in enumerate(results[:5], 1):  # 只显示前5个结果
            print(f"  {i}. {result.get('title', 'N/A')} ({result.get('group', 'N/A')})")
            print(f"     URL: {result.get('url', 'N/A')}")
        
        if len(results) > 5:
            print(f"  ... 还有 {len(results) - 5} 个结果")
        
        print()

if __name__ == "__main__":
    test_search()
