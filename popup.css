* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    width: 320px;
    padding: 15px;
  }
  
  .container {
    width: 100%;
  }
  
  h1 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #4a6bff;
    text-align: center;
  }
  
  .form-group {
    margin-bottom: 12px;
  }
  
  label {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 500;
  }
  
  input,
  select,
  textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 13px;
  }
  
  textarea {
    height: 60px;
    resize: vertical;
  }
  
  .select-container {
    display: flex;
    gap: 8px;
  }
  
  .select-container select {
    flex: 1;
  }
  
  .select-container button {
    padding: 0 8px;
    background-color: #f0f0f0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
  }
  
  .select-container button:hover {
    background-color: #e0e0e0;
  }
  
  .image-preview {
    width: 60px;
    height: 60px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  .image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  
  .form-actions {
    margin-top: 15px;
  }
  
  button {
    width: 100%;
    padding: 8px;
    background-color: #4a6bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
  }
  
  button:hover {
    background-color: #3a5bef;
  }
  
  .status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
    display: none;
  }
  
  .status.success {
    background-color: #e6f7e6;
    color: #2e7d32;
    display: block;
  }
  
  .status.error {
    background-color: #fdecea;
    color: #d32f2f;
    display: block;
  }
  
  .paste-btn {
    margin-top: 8px;
    padding: 5px 10px;
    background-color: #f0f0f0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: block;
    width: 100%;
  }
  
  .paste-btn:hover {
    background-color: #e0e0e0;
  }
  
  .status.info {
    background-color: #e6f7ff;
    color: #1890ff;
    display: block;
  }
  
  