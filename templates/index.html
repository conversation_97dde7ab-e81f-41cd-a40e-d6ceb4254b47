<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的导航</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 移动端菜单切换按钮 -->
        <button class="mobile-menu-toggle" id="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </button>

        <!-- 移动端侧边栏遮罩 -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- 左侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="db-selector">
                    <div class="current-db" id="current-db">
                        <span>{{ current_db }}</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="db-dropdown" id="db-dropdown">
                        {% for db in databases %}
                        <div class="db-item {% if db == current_db %}active{% endif %}" data-db="{{ db }}">{{ db }}</div>
                        {% endfor %}
                        <div class="db-item new-db">
                            <i class="btn primary  fas fa-plus"></i> 新建库
                        </div>
                        <div class="db-item clean-navigation">
                            <i class="fas fa-broom"></i> 清理导航
                        </div>
                        <div class="db-item clean-images">
                            <i class="fas fa-broom"></i> 清理图片
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="groups-container">
                <div class="groups-header">
                    <h3>分组</h3>
                    <button class="add-group-btn" id="add-group-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <ul id="groups-list" class="groups-list">
                    {% for group in groups %}
                    <li class="group-item {% if group == active_group %}active{% endif %}" data-group="{{ group }}">
                        <span class="group-name">{{ group }}</span>
                        <span class="group-count" id="group-count-{{ group }}">0</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <header class="main-header">
                <div class="search-container">
                    <input type="text" id="search-input" placeholder="搜索网站...">
                    <button id="search-btn"><i class="fas fa-search"></i></button>
                </div>
                <div class="actions">
                    <button id="add-website-btn" class="btn primary"><i class="fas fa-plus"></i> 添加网站</button>
                    <button id="manage-btn" class="btn secondary"><i class="fas fa-cog"></i> 管理</button>
                    <button id="settings-btn" class="btn secondary"><i class="fas fa-palette"></i> 设置</button>
                </div>
            </header>

            <div class="content-area">
                <div id="websites-container" class="websites-grid">
                    <!-- 网站卡片将通过JavaScript动态加载 -->
                </div>
                
                <div id="search-results" class="websites-grid" style="display: none;">
                    <!-- 搜索结果将在这里显示 -->
                </div>
                
                <div id="loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                </div>
                
                <!-- 管理模式工具栏 -->
                <div id="management-toolbar" class="management-toolbar" style="display: none;">
                    <div class="selection-info">
                        已选择 <span id="selected-count">0</span> 项，拖动可以移动到其他组
                    </div>
                    <div class="toolbar-actions">
                        <button id="cancel-selection-btn" class="btn small">取消</button>
                        <button id="delete-selected-btn" class="btn small danger">删除</button>
                    </div>
                </div>
            </div>


        </main>
                    <!-- iframe容器 >
                    <div class="iframe-container" style="position: relative; margin-top: 10px;">
                        <div class="iframe-controls" style="position: absolute; top: 10px; right: 10px; z-index: 10;">
                            <button id="iframe-width-decrease" class="btn small" style="margin-right: 5px;">-</button>
                            <button id="iframe-width-increase" class="btn small">+</button>
                        </div>
                        <iframe id="nullboard-iframe" src="{{ url_for('static', filename='nullboard.html') }}" 
                                style="width: 600px; height: 100%; border: 1px solid #ccc; transition: width 0.3s ease;">
                        </iframe>
                    </div-->
    </div>

    <!-- 添加网站模态框 -->
    <div id="add-website-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>添加网站</h2>
            <form id="add-website-form">
                <input type="hidden" id="website-db" name="db" value="{{ current_db }}">
                <div class="form-group">
                    <label for="website-title">网站名称</label>
                    <input type="text" id="website-title" name="title" required>
                </div>
                <div class="form-group">
                    <label for="website-url">网站地址</label>
                    <input type="url" id="website-url" name="url" required>
                </div>
                <div class="form-group">
                    <label for="website-group">分组</label>
                    <div class="select-container">
                        <select id="website-group" name="group">
                            {% for group in groups %}
                            <option value="{{ group }}">{{ group }}</option>
                            {% endfor %}
                        </select>
                        <button type="button" id="new-group-btn" class="btn small">新建分组</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="website-note">备注</label>
                    <textarea id="website-note" name="note"></textarea>
                </div>
                <div class="form-group">
                    <label>图标</label>
                    <div class="image-upload">
                        <div class="preview-container">
                            <img id="image-preview" src="{{ url_for('static', filename='img/default-icon.png') }}" alt="预览">
                        </div>
                        <div class="upload-actions">
                            <input type="file" id="website-image" name="image" accept="image/*">
                            <label for="website-image" class="btn small">选择图片</label>
                            <button type="button" id="paste-image-btn" class="btn small">粘贴图片</button>
                            <input type="hidden" id="image-data" name="image_data">
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn primary">保存</button>
                    <button type="button" class="btn cancel" id="cancel-add">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 新建分组模态框 -->
    <div id="add-group-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>新建分组</h2>
            <form id="add-group-form">
                <div class="form-group">
                    <label for="group-name">分组名称</label>
                    <input type="text" id="group-name" name="name" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn primary">保存</button>
                    <button type="button" class="btn cancel" id="cancel-add-group">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 新建数据库模态框 -->
    <div id="add-db-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>新建数据库</h2>
            <form id="add-db-form">
                <div class="form-group">
                    <label for="db-name">数据库名称</label>
                    <input type="text" id="db-name" name="name" required pattern="[a-zA-Z0-9_-]+" title="只能包含字母、数字、下划线和连字符">
                    <small>只能包含字母、数字、下划线和连字符</small>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn primary">保存</button>
                    <button type="button" class="btn cancel" id="cancel-add-db">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 清理图片模态框 -->
    <div id="clean-images-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>清理未使用的图片</h2>
            <div id="unused-images-container" class="unused-images-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在扫描未使用的图片...</p>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn primary" id="clean-images-btn" disabled>清理选中图片</button>
                <button type="button" class="btn cancel" id="cancel-clean-images">取消</button>
            </div>
        </div>
    </div>
    
    <!-- 图片预览模态框 -->
    <div id="image-viewer-modal" class="modal image-viewer-modal">
        <div class="image-viewer-container">
            <div class="image-viewer-header">
                <h3 id="image-viewer-title">图片预览</h3>
                <span class="close">&times;</span>
            </div>
            <div class="image-viewer-content">
                <div class="image-viewer-main">
                    <button class="nav-btn prev-btn" id="prev-image-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="image-container" id="image-container">
                        <img id="viewer-image" src="/placeholder.svg" alt="预览图片">
                    </div>
                    <button class="nav-btn next-btn" id="next-image-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="image-viewer-controls">
                    <button class="control-btn" id="flip-h-btn" title="水平翻转">
                        <i class="fas fa-arrows-alt-h"></i>
                    </button>
                    <button class="control-btn" id="flip-v-btn" title="垂直翻转">
                        <i class="fas fa-arrows-alt-v"></i>
                    </button>
                    <button class="control-btn" id="zoom-in-btn" title="放大">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="control-btn" id="zoom-out-btn" title="缩小">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button class="control-btn" id="zoom-reset-btn" title="重置">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>界面设置</h2>
            <form id="settings-form">
                <div class="settings-section">
                    <h3>卡片设置</h3>
                    <div class="form-group">
                        <label for="card-size">卡片大小</label>
                        <select id="card-size" name="cardSize">
                            <option value="small">小</option>
                            <option value="medium" selected>中</option>
                            <option value="large">大</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="card-gap">卡片间距</label>
                        <input type="range" id="card-gap" name="cardGap" min="10" max="30" value="20">
                        <span class="range-value">20px</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>分组设置</h3>
                    <div class="form-group">
                        <label for="group-height">分组项高度</label>
                        <input type="range" id="group-height" name="groupHeight" min="35" max="60" value="45">
                        <span class="range-value">45px</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>颜色设置</h3>
                    <div class="form-group">
                        <label for="bg-color">背景颜色</label>
                        <input type="color" id="bg-color" name="bgColor" value="#f9fafc">
                    </div>
                    <div class="form-group">
                        <label for="card-bg-color">卡片背景颜色</label>
                        <input type="color" id="card-bg-color" name="cardBgColor" value="#ffffff">
                    </div>
                    <div class="form-group">
                        <label for="text-color">文本颜色</label>
                        <input type="color" id="text-color" name="textColor" value="#333333">
                    </div>
                    <div class="form-group">
                        <label for="primary-color">主题颜色</label>
                        <input type="color" id="primary-color" name="primaryColor" value="#4a6bff">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn secondary" id="reset-settings">重置默认</button>
                    <button type="button" class="btn cancel" id="cancel-settings">取消</button>
                    <button type="submit" class="btn primary">保存设置</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 浮动搜索框 >
    <div class="floating-search-container">
        <input type="text" id="floating-search-input" placeholder="搜索所有分组网站...">
        <button id="floating-search-btn"><i class="fas fa-search"></i></button>
    </div-->

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script2.js') }}"></script>
</body>
</html>

