// 这个脚本在网页中运行，可以用来获取更多网页信息和注入UI

// 全局变量
let injectedPopup = null
let selectedText = ""

// 监听来自扩展的消息
if (typeof chrome !== "undefined" && chrome.runtime) {
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "getPageInfo") {
      // 获取页面信息
      const pageInfo = {
        title: document.title,
        description: getMetaDescription(),
        // 可以添加更多信息
      }

      sendResponse(pageInfo)
    } else if (request.action === "showInjectedPopup") {
      // 显示注入的弹出窗口
      showInjectedPopup(request.data)
      sendResponse({ success: true })
    } else if (request.action === "getSelectedText") {
      // 获取选中的文本
      sendResponse({ text: selectedText })
    }

    return true // 保持消息通道开放，以便异步响应
  })
} else {
  console.warn("Chrome runtime environment not detected.")
}

// 获取页面描述
function getMetaDescription() {
  const metaDesc = document.querySelector('meta[name="description"]')
  return metaDesc ? metaDesc.getAttribute("content") : ""
}

// 页面加载完成后执行
document.addEventListener("DOMContentLoaded", () => {
  // 监听文本选择
  document.addEventListener("mouseup", () => {
    selectedText = window.getSelection().toString().trim()
  })


})
  // 添加图片浮动按钮
  //setInterval(()=>{addImageFloatingButtons()}, 500)
  //setTimeout(() => {    addImageFloatingButtons()  }, 500);
// 添加图片浮动按钮
function addImageFloatingButtons() {
  // 获取所有图片
  const images = document.querySelectorAll("img")

  images.forEach((img) => {
    if (img.dataset.floatingButtonAdded) return
    // 检查图片尺寸
    if (img.naturalWidth >= 150 && img.naturalHeight >= 150) {
      // 创建容器
      const container = document.createElement("div")
      container.className = "nav-image-container"
    img.dataset.floatingButtonAdded = true;
      // 将图片包装在容器中
      //img.parentNode.insertBefore(container, img)
      //container.appendChild(img)

      // 创建浮动按钮
      const button = document.createElement("button")
      button.className = "nav-add-btn"
      button.innerHTML = '<i class="fas fa-plus"></i>'
      button.title = "添加到导航"

      // 添加按钮点击事件
      button.addEventListener("click", async (e) => {
        e.preventDefault()
        e.stopPropagation()

        // 显示加载指示器
        const loadingIndicator = document.createElement("div")
        loadingIndicator.className = "nav-loading-indicator"
        loadingIndicator.innerHTML = '<div class="spinner"></div>'
        container.appendChild(loadingIndicator)
        button.style.display = "none"

        try {
          // 获取图片的base64数据
          const imgBase64 = await getImageAsBase64(img.src)

          // 获取图片信息
          const imgData = {
            title: document.title,
            url: window.location.href,
            img: imgBase64 || img.src, // 如果获取base64失败，则使用原始URL
            note: `图片来源: ${window.location.href}`,
          }

          // 显示注入的弹出窗口
          showInjectedPopup(imgData)
        } catch (error) {
          console.error("处理图片失败:", error)
          alert("无法处理图片，请尝试其他图片")
        } finally {
          // 移除加载指示器
          container.removeChild(loadingIndicator)
          button.style.display = "flex"
        }
      })

      // 添加按钮到容器
      container.appendChild(button)
      img.parentNode.appendChild(button);

      // 添加样式
      
    }
  })
}
addFloatingButtonStyles()
// 将图片URL转换为base64
async function getImageAsBase64(imgUrl) {
    try {
      // 创建一个canvas元素
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")
  
      // 创建一个新的图片元素
      const img = new Image()
      img.crossOrigin = "anonymous" // 尝试解决跨域问题
  
      // 等待图片加载
      await new Promise((resolve, reject) => {
        img.onload = resolve
        img.onerror = reject
        img.src = imgUrl
      })
  
      // 设置canvas大小与图片相同
      canvas.width = img.width
      canvas.height = img.height
  
      // 在canvas上绘制图片
      ctx.drawImage(img, 0, 0)
  
      // 将canvas内容转换为base64
      return canvas.toDataURL("image/png")
    } catch (error) {
      console.error("获取图片数据失败:", error)
      // 如果失败，返回null
      return null
    }
  }
// 添加浮动按钮样式
function addFloatingButtonStyles() {
  // 检查是否已添加样式
  if (document.getElementById("nav-floating-styles")) return
console.log("Adding floating button styles...")
  const style = document.createElement("style")
  style.id = "nav-floating-styles"
  style.textContent = `
    .nav-image-container {
 
      height:100%;
    }
    
    .nav-add-btn {
      position: absolute;
      top: 50%;
      right: 10px;
      background-color: rgba(74, 107, 255, 0.8);
      color: white;
      border: none;
      width: 2px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 1000;
      opacity: 0.2;
      transition: opacity 0.3s ease;
    }
    
    .nav-add-btn:hover {
      background-color: rgba(74, 107, 255, 1);
      opacity: 1;
    }
    
    .nav-image-container:hover .nav-add-btn {
      opacity: 1;
    }
    
    /* 注入的弹出窗口样式 */
    #injected-popup {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 350px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
      z-index: 2147483647;
      font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
      padding: 20px;
    }
    
    #injected-popup-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 2147483646;
    }
    
    #injected-popup h2 {
      font-size: 18px;
      margin-bottom: 15px;
      color: #4a6bff;
      text-align: center;
    }
    
    #injected-popup .form-group {
      margin-bottom: 12px;
    }
    
    #injected-popup label {
      display: block;
      margin-bottom: 5px;
      font-size: 13px;
      font-weight: 500;
    }
    
    #injected-popup input,
    #injected-popup select,
    #injected-popup textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 13px;
    }
    
    #injected-popup textarea {
      height: 60px;
      resize: vertical;
    }
    
    #injected-popup .select-container {
      display: flex;
      gap: 8px;
    }
    
    #injected-popup .select-container select {
      flex: 1;
    }
    
    #injected-popup .select-container button {
      padding: 0 8px;
      background-color: #f0f0f0;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    
    #injected-popup .image-preview {
      width: 60px;
      height: 60px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      margin-bottom: 8px;
    }
    
    #injected-popup .image-preview img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    
    #injected-popup .form-actions {
      display: flex;
      gap: 8px;
      margin-top: 15px;
    }
    
    #injected-popup button {
      flex: 1;
      padding: 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
    
    #injected-popup button.primary {
      background-color: #4a6bff;
      color: white;
      border: none;
    }
    
    #injected-popup button.primary:hover {
      background-color: #3a5bef;
    }
    
    #injected-popup button.cancel {
      background-color: #f0f0f0;
      color: #333;
      border: 1px solid #e0e0e0;
    }
    
    #injected-popup button.cancel:hover {
      background-color: #e0e0e0;
    }
    
    #injected-popup .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      font-size: 13px;
      text-align: center;
      display: none;
    }
    
    #injected-popup .status.success {
      background-color: #e6f7e6;
      color: #2e7d32;
      display: block;
    }
    
    #injected-popup .status.error {
      background-color: #fdecea;
      color: #d32f2f;
      display: block;
    }
    
    #injected-popup .db-selector {
      margin-bottom: 12px;
    }
    
    #injected-popup .db-selector label {
      display: block;
      margin-bottom: 5px;
      font-size: 13px;
      font-weight: 500;
    }
  `

  document.body.appendChild(style)
}

// 显示注入的弹出窗口
function showInjectedPopup(data = {}) {
  // 如果已存在，先移除
  removeInjectedPopup()

  // 创建遮罩层
  const overlay = document.createElement("div")
  overlay.id = "injected-popup-overlay"

  // 创建弹出窗口
  const popup = document.createElement("div")
  popup.id = "injected-popup"

  // 设置HTML内容
  popup.innerHTML = `
    <h2>添加到导航</h2>
    <form id="injected-form">
      <div class="form-group db-selector">
        <label for="injected-db">选择库</label>
        <select id="injected-db" name="db">
          <!-- 数据库选项将通过AJAX加载 -->
        </select>
      </div>
      <div class="form-group">
        <label for="injected-title">网站名称</label>
        <input type="text" id="injected-title" name="title" value="${data.title || ""}" required>
      </div>
      <div class="form-group">
        <label for="injected-url">网站地址</label>
        <input type="url" id="injected-url" name="url" value="${data.url || ""}" required>
      </div>
      <div class="form-group">
        <label for="injected-group">分组</label>
        <div class="select-container">
          <select id="injected-group" name="group" required>
            <!-- 分组选项将通过AJAX加载 -->
          </select>
          <button type="button" id="injected-new-group-btn">新建</button>
        </div>
      </div>
      <div class="form-group">
        <label for="injected-note">备注</label>
        <textarea id="injected-note" name="note">${data.note || ""}</textarea>
      </div>
      <div class="form-group">
        <label>图标</label>
        <div class="image-preview">
          <img id="injected-image-preview" src="${data.img || (typeof chrome !== "undefined" && chrome.runtime ? "chrome-extension://" + chrome.runtime.id + "/icons/default-icon.png" : "/icons/default-icon.png")}" alt="预览">
        </div>
      </div>
      <div class="form-actions">
        <button type="submit" class="primary">保存</button>
        <button type="button" class="cancel" id="injected-cancel-btn">取消</button>
      </div>
      <div id="injected-status" class="status"></div>
    </form>
  `

  // 添加到页面
  document.body.appendChild(overlay)
  document.body.appendChild(popup)

  // 保存引用
  injectedPopup = popup

  // 加载数据库和分组
  loadDatabases()

  // 添加事件监听
  document.getElementById("injected-cancel-btn").addEventListener("click", removeInjectedPopup)
  overlay.addEventListener("click", removeInjectedPopup)

  // 新建分组按钮点击事件
  document.getElementById("injected-new-group-btn").addEventListener("click", createNewGroup)

  // 表单提交事件
  document.getElementById("injected-form").addEventListener("submit", submitInjectedForm)
}

// 移除注入的弹出窗口
function removeInjectedPopup() {
  const popup = document.getElementById("injected-popup")
  const overlay = document.getElementById("injected-popup-overlay")

  if (popup) popup.remove()
  if (overlay) overlay.remove()

  injectedPopup = null
}

// 加载数据库列表
function loadDatabases() {
  // 使用no-cors模式来避免CORS问题
  fetch("http://localhost:4157/api/databases", {
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      return response.json()
    })
    .then((databases) => {
      const dbSelect = document.getElementById("injected-db")
      dbSelect.innerHTML = ""

      // 获取上次选择的数据库
      if (typeof chrome !== "undefined" && chrome.storage) {
        chrome.storage.local.get(["lastSelectedDb"], (result) => {
          const lastDb = result.lastSelectedDb || "data"

          databases.forEach((db) => {
            const option = document.createElement("option")
            option.value = db
            option.textContent = db
            option.selected = db === lastDb
            dbSelect.appendChild(option)
          })

          // 加载分组
          loadGroups(lastDb)

          // 数据库变更事件
          dbSelect.addEventListener("change", () => {
            loadGroups(dbSelect.value)
            // 保存选择
            chrome.storage.local.set({ lastSelectedDb: dbSelect.value })
          })
        })
      } else {
        // 如果chrome.storage不存在，则使用默认值加载分组
        databases.forEach((db) => {
          const option = document.createElement("option")
          option.value = db
          option.textContent = db
          option.selected = db === "data"
          dbSelect.appendChild(option)
        })

        loadGroups("data")

        // 数据库变更事件
        dbSelect.addEventListener("change", () => {
          loadGroups(dbSelect.value)
        })
      }
    })
    .catch((error) => {
      console.error("加载数据库出错:", error)
      showInjectedStatus("无法连接到导航网站，请确保网站已启动", "error")

      // 如果出错，添加一个默认选项
      const dbSelect = document.getElementById("injected-db")
      dbSelect.innerHTML = ""
      const option = document.createElement("option")
      option.value = "data"
      option.textContent = "data"
      dbSelect.appendChild(option)

      // 加载默认分组
      loadGroups("data")
    })
}

// 加载分组列表
function loadGroups(db = "data") {
  fetch(`http://localhost:4157/api/groups?db=${db}`, {
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      return response.json()
    })
    .then((groups) => {
      const groupSelect = document.getElementById("injected-group")
      groupSelect.innerHTML = ""

      if (groups.length === 0) {
        // 如果没有分组，添加默认分组
        const option = document.createElement("option")
        option.value = "工具"
        option.textContent = "工具"
        groupSelect.appendChild(option)
      } else {
        groups.forEach((group) => {
          const option = document.createElement("option")
          option.value = group
          option.textContent = group
          groupSelect.appendChild(option)
        })
      }
    })
    .catch((error) => {
      console.error("加载分组出错:", error)
      showInjectedStatus("无法连接到导航网站，请确保网站已启动", "error")

      // 如果出错，添加一个默认选项
      const groupSelect = document.getElementById("injected-group")
      groupSelect.innerHTML = ""
      const option = document.createElement("option")
      option.value = "工具"
      option.textContent = "工具"
      groupSelect.appendChild(option)
    })
}

// 创建新分组
function createNewGroup() {
  const groupName = prompt("请输入新分组名称:")
  if (!groupName || groupName.trim() === "") return

  const db = document.getElementById("injected-db").value

  fetch("http://localhost:4157/api/add-group", {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    body: JSON.stringify({ 
      name: groupName.trim(),
      db: db
    }),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      return response.json()
    })
    .then((data) => {
      if (data.success) {
        // 添加新分组到下拉列表
        const option = document.createElement("option")
        option.value = groupName.trim()
        option.textContent = groupName.trim()
        document.getElementById("injected-group").appendChild(option)
        document.getElementById("injected-group").value = groupName.trim()
      } else {
        showInjectedStatus(data.error || "添加分组失败", "error")
      }
    })
    .catch((error) => {
      console.error("添加分组出错:", error)
      showInjectedStatus("添加分组时出错", "error")
    })
}

// 提交注入表单
function submitInjectedForm(e) {
  e.preventDefault()

  const title = document.getElementById("injected-title").value.trim()
  const url = document.getElementById("injected-url").value.trim()
  const group = document.getElementById("injected-group").value
  const note = document.getElementById("injected-note").value.trim()
  const db = document.getElementById("injected-db").value
  const img = document.getElementById("injected-image-preview").src

  if (!title || !url || !group) {
    showInjectedStatus("请填写必填字段", "error")
    return
  }

  // 禁用提交按钮
  document.querySelector("#injected-form button[type='submit']").disabled = true

  // 准备要发送的数据
  const data = {
    title: title,
    url: url,
    group: group,
    note: note,
    img: img,
    db: db,
  }

  // 发送到服务器
  fetch("http://localhost:4157/api/extension/add", {
    method: "POST",
    mode: "cors",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`)
      }
      return response.json()
    })
    .then((result) => {
      if (result.success) {
        showInjectedStatus("添加成功！", "success")
        setTimeout(() => {
          removeInjectedPopup()
        }, 1500)
      } else {
        showInjectedStatus(result.error || "添加失败", "error")
        document.querySelector("#injected-form button[type='submit']").disabled = false
      }
    })
    .catch((error) => {
      console.error("添加网站出错:", error)
      showInjectedStatus("无法连接到导航网站，请确保网站已启动", "error")
      document.querySelector("#injected-form button[type='submit']").disabled = false
    })
}

// 显示注入状态信息
function showInjectedStatus(message, type) {
  const statusDiv = document.getElementById("injected-status")
  if (!statusDiv) return

  statusDiv.textContent = message
  statusDiv.className = "status " + type

  if (type === "success") {
    setTimeout(() => {
      statusDiv.className = "status"
    }, 3000)
  }
}
