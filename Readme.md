## 本程序有哪些功能：

1、点击插件，可以保存网站到本地，默认是保存网站icon，也可以粘贴剪贴板里的图片为缩略图

2、点击图片上的浮动按钮，可以采集该图片为网站缩略图

#### 其他功能：

1、多个库的功能，当导航、图片太多时，可以新建导航，方便管理

2、多选导航可以拖动到某个组，多选可以批量删除

3、点击图片可以预览缩放翻转

4、可以预览备注，当作简单的笔记工具

5、可以清理未引用的图片，空白的组，重复的网站

插件界面：

![img](templates/插件界面.png "插件界面")

导航界面：

![](templates/界面截图.png)

## 使用方法：

#### 1、安装python3.10

如果你已经装了其他版本也可以试试，如果不行就安装python3.10

下载地址：[https://www.python.org/downloads/release/python-31015/](https://www.python.org/downloads/release/python-31015/)

（如果你用的是别的版本，以下的python3.10改成你对应的拌好或直接用python）

#### 2、安装依赖

Windows系统：    在资源管理器栏输入cmd，打开终端

MacOs系统：   在启动台-其他里找到终端

输入

    ``pip3.10 install flask``

``pip3.10 install flask-cors``

``pip3.10 install Werkzeug``

#### 3、下载本程序，或在终端里git clone本程序地址

然后windows：打开本程序的文件夹，在地址栏输入cmd，打开终端，输入python3.10 app.py

在浏览器打开 http://127.0.0.1:4157

#### 4、安装浏览器插件

在浏览器插件管理，加载已解压的插件，选择该程序文件夹即可
