from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
import json
import os
import uuid
import base64
from datetime import datetime
import re
import shutil
from werkzeug.utils import secure_filename
from flask_cors import CORS  # 添加CORS支持

app = Flask(__name__)
# 启用CORS，允许所有来源的跨域请求
CORS(app, resources={r"/api/*": {"origins": "*"}})

app.config['UPLOAD_FOLDER'] = 'static/img'
DEFAULT_DB = 'data'

# 确保数据文件和图片目录存在
def ensure_files_exist(db_name=DEFAULT_DB):
    data_file = f'{db_name}.json'
    if not os.path.exists(data_file):
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump({}, f, ensure_ascii=False)
    
    img_folder = f'static/img/{db_name}'
    if not os.path.exists(img_folder):
        os.makedirs(img_folder)
    
    # 确保默认图片存在
    default_icon = 'static/img/default-icon.png'
    if not os.path.exists(default_icon):
        # 创建一个简单的默认图标
        with open(default_icon, 'wb') as f:
            # 这里可以放一个简单的默认图标的二进制数据
            pass

# 加载数据
def load_data(db_name=DEFAULT_DB):
    ensure_files_exist(db_name)
    data_file = f'{db_name}.json'
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        return {}

# 保存数据
def save_data(data, db_name=DEFAULT_DB):
    data_file = f'{db_name}.json'
    with open(data_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

# 主页
@app.route('/')
def index():
    db_name = request.args.get('db', DEFAULT_DB)
    data = load_data(db_name)
    groups = list(data.keys())
    active_group = request.args.get('group', groups[0] if groups else None)
    
    # 获取所有可用的数据库
    databases = get_all_databases()
    
    return render_template('index.html', 
                          data=data, 
                          groups=groups, 
                          active_group=active_group, 
                          current_db=db_name,
                          databases=databases)

# 获取分组数据
@app.route('/api/group/<group_name>')
def get_group(group_name):
    db_name = request.args.get('db', DEFAULT_DB)
    data = load_data(db_name)
    if group_name in data:
        return jsonify(data[group_name])
    return jsonify([])

# 获取所有分组
@app.route('/api/groups')
def get_groups():
    db_name = request.args.get('db', DEFAULT_DB)
    data = load_data(db_name)
    return jsonify(list(data.keys()))

# 添加新网站
@app.route('/api/add', methods=['POST'])
def add_website():
    db_name = request.form.get('db', DEFAULT_DB)
    data = load_data(db_name)
    
    title = request.form.get('title')
    url = request.form.get('url')
    group = request.form.get('group')
    note = request.form.get('note', '')
    
    # 处理图片
    img_path = 'img/default-icon.png'
    if 'image' in request.files and request.files['image'].filename:
        image = request.files['image']
        filename = f"{int(datetime.now().timestamp() * 1000)}.png"
        img_folder = f'img/{db_name}'
        os.makedirs(f'static/{img_folder}', exist_ok=True)
        image_path = os.path.join(app.config['UPLOAD_FOLDER'], db_name, filename)
        image.save(image_path)
        img_path = f"{img_folder}/{filename}"
    elif request.form.get('image_data'):
        # 处理Base64图片数据
        image_data = request.form.get('image_data').split(',')[1]
        filename = f"{int(datetime.now().timestamp() * 1000)}.png"
        img_folder = f'img/{db_name}'
        os.makedirs(f'static/{img_folder}', exist_ok=True)
        image_path = os.path.join(app.config['UPLOAD_FOLDER'], db_name, filename)
        
        with open(image_path, "wb") as f:
            f.write(base64.b64decode(image_data))
        
        img_path = f"{img_folder}/{filename}"
    
    # 确保分组存在
    if group not in data:
        data[group] = []
    
    # 添加新网站
    data[group].append({
        "title": title,
        "url": url,
        "img": img_path,
        "note": note
    })
    
    save_data(data, db_name)
    return jsonify({"success": True})

# 删除网站
@app.route('/api/delete', methods=['POST'])
def delete_website():
    db_name = request.json.get('db', DEFAULT_DB)
    data = load_data(db_name)
    group = request.json.get('group')
    indices = request.json.get('indices', [])
    
    if not indices and 'index' in request.json:
        indices = [request.json.get('index')]
    
    if group in data and indices:
        # 排序并反转，以便从后向前删除，避免索引变化
        indices.sort(reverse=True)
        
        for index in indices:
            if 0 <= index < len(data[group]):
                # 如果图片不是默认图片，则删除
                img_path = data[group][index]['img']
                if img_path != 'img/default-icon.png' and os.path.exists(f"static/{img_path}"):
                    os.remove(f"static/{img_path}")
                
                # 删除网站
                data[group].pop(index)
        
        # 如果分组为空，删除分组
        if not data[group]:
            del data[group]
        
        save_data(data, db_name)
        return jsonify({"success": True})
    
    return jsonify({"success": False, "error": "Website not found"})

# 搜索
@app.route('/api/search')
def search():
    try:
        query = request.args.get('q', '').lower().strip()
        db_name = request.args.get('db', DEFAULT_DB)

        if not query:
            return jsonify([])

        data = load_data(db_name)
        results = []

        for group, websites in data.items():
            for website in websites:
                # 确保必要的字段存在且不为None
                title = (website.get('title') or '').lower()
                url = (website.get('url') or '').lower()
                note = (website.get('note') or '').lower()

                # 搜索title和url字段
                if query in title or query in url or query in note:
                    website_copy = website.copy()
                    website_copy['group'] = group
                    results.append(website_copy)

        return jsonify(results)
    except Exception as e:
        print(f"Search error: {str(e)}")
        return jsonify({"error": "搜索时出错"}), 500

# 添加新分组
@app.route('/api/add-group', methods=['POST'])
def add_group():
    group_name = request.json.get('name')
    db_name = request.json.get('db', DEFAULT_DB)
    data = load_data(db_name)
    
    if group_name and group_name not in data:
        data[group_name] = []
        save_data(data, db_name)
        return jsonify({"success": True})
    
    return jsonify({"success": False, "error": "Group already exists or invalid name"})

# API端点用于浏览器扩展
@app.route('/api/extension/add', methods=['POST'])
def extension_add():
    try:
        db_name = request.json.get('db', DEFAULT_DB)
        data = load_data(db_name)
        
        title = request.json.get('title')
        url = request.json.get('url')
        group = request.json.get('group')
        img_data = request.json.get('img')
        note = request.json.get('note', '')
        
        # 确保分组存在
        if group not in data:
            data[group] = []
        
        # 处理图片
        img_path = 'img/default-icon.png'
        if img_data and img_data.startswith('data:'):
            # 处理Base64图片数据
            img_format = re.search(r'data:image/(\w+);base64,', img_data)
            if img_format:
                ext = img_format.group(1)
                image_data = img_data.split(',')[1]
                filename = f"{int(datetime.now().timestamp() * 1000)}.{ext}"
                img_folder = f'img/{db_name}'
                os.makedirs(f'static/{img_folder}', exist_ok=True)
                image_path = os.path.join(app.config['UPLOAD_FOLDER'], db_name, filename)
                
                with open(image_path, "wb") as f:
                    f.write(base64.b64decode(image_data))
                
                img_path = f"{img_folder}/{filename}"
        
        # 添加新网站
        data[group].append({
            "title": title,
            "url": url,
            "img": img_path,
            "note": note
        })
        
        save_data(data, db_name)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

def get_all_databases():
    databases = []
    for file in os.listdir():
        if file.endswith('.json') and os.path.isfile(file) and not file.startswith('.') and file != 'manifest.json':
            db_name = file.rsplit('.', 1)[0]
            databases.append(db_name)
    return databases

@app.route('/api/move', methods=['POST'])
def move_website():
    db_name = request.json.get('db', DEFAULT_DB)
    data = load_data(db_name)
    from_group = request.json.get('fromGroup')
    to_group = request.json.get('toGroup')
    indices = request.json.get('indices', [])
    
    if from_group in data and to_group and indices:
        # 确保目标分组存在
        if to_group not in data:
            data[to_group] = []
        
        # 收集要移动的项目
        items_to_move = []
        indices.sort(reverse=True)  # 从后向前处理
        
        for index in indices:
            if 0 <= index < len(data[from_group]):
                items_to_move.append(data[from_group][index])
                data[from_group].pop(index)
        
        # 添加到目标分组
        data[to_group].extend(items_to_move)
        
        # 如果源分组为空，删除它
        if not data[from_group]:
            del data[from_group]
        
        save_data(data, db_name)
        return jsonify({"success": True})
    
    return jsonify({"success": False, "error": "Invalid move operation"})

@app.route('/api/databases')
def get_databases():
    return jsonify(get_all_databases())

@app.route('/api/create-database', methods=['POST'])
def create_database():
    db_name = request.json.get('name')
    if not db_name or not re.match(r'^[a-zA-Z0-9_-]+$', db_name):
        return jsonify({"success": False, "error": "Invalid database name"})
    
    # 检查是否已存在
    if os.path.exists(f'{db_name}.json'):
        return jsonify({"success": False, "error": "Database already exists"})
    
    # 创建新数据库
    ensure_files_exist(db_name)
    
    return jsonify({"success": True})

@app.route('/api/unused-images')
def get_unused_images():
    db_name = request.args.get('db', DEFAULT_DB)
    data = load_data(db_name)
    
    # 收集所有使用的图片
    used_images = set()
    for group, websites in data.items():
        for website in websites:
            if website['img'] != 'img/default-icon.png':
                used_images.add(website['img'])
    
    # 检查图片目录
    img_folder = f'static/img/{db_name}'
    unused_images = []
    
    if os.path.exists(img_folder):
        for file in os.listdir(img_folder):
            img_path = f'img/{db_name}/{file}'
            if img_path not in used_images:
                unused_images.append({
                    'path': img_path,
                    'filename': file
                })
    
    return jsonify(unused_images)

@app.route('/api/clean-images', methods=['POST'])
def clean_images():
    db_name = request.json.get('db', DEFAULT_DB)
    images = request.json.get('images', [])
    
    deleted_count = 0
    for img_path in images:
        full_path = f'static/{img_path}'
        if os.path.exists(full_path):
            os.remove(full_path)
            deleted_count += 1
    
    return jsonify({"success": True, "deleted": deleted_count})

@app.route('/api/check-duplicates')
def check_duplicates():
    db_name = request.args.get('db', DEFAULT_DB)
    data = load_data(db_name)
    
    # 检查重复URL
    url_map = {}
    duplicates = []
    
    for group, websites in data.items():
        for index, website in enumerate(websites):
            url = website['url']
            if url in url_map:
                # 已存在相同URL
                duplicates.append({
                    'type': 'duplicate',
                    'url': url,
                    'group1': url_map[url]['group'],
                    'index1': url_map[url]['index'],
                    'title1': url_map[url]['title'],
                    'group2': group,
                    'index2': index,
                    'title2': website['title']
                })
            else:
                url_map[url] = {
                    'group': group,
                    'index': index,
                    'title': website['title']
                }
    
    # 检查空白分组
    empty_groups = []
    for group, websites in data.items():
        if len(websites) == 0:
            empty_groups.append({
                'type': 'empty_group',
                'group': group
            })
    
    return jsonify({
        'duplicates': duplicates,
        'empty_groups': empty_groups
    })

@app.route('/api/check-url-exists')
def check_url_exists():
    url = request.args.get('url', '')
    db_name = request.args.get('db', DEFAULT_DB)
    
    if not url:
        return jsonify({'exists': False})
    
    data = load_data(db_name)
    
    for group, websites in data.items():
        for website in websites:
            if website['url'] == url:
                return jsonify({
                    'exists': True,
                    'website': website,
                    'group': group
                })
    
    return jsonify({'exists': False})

@app.route('/api/website')
def get_website():
    db_name = request.args.get('db', DEFAULT_DB)
    group = request.args.get('group')
    index = request.args.get('index')
    
    if not group or index is None:
        return jsonify(None)
    
    try:
        index = int(index)
        data = load_data(db_name)
        
        if group in data and 0 <= index < len(data[group]):
            return jsonify(data[group][index])
    except:
        pass
    
    return jsonify(None)

@app.route('/api/edit', methods=['POST'])
def edit_website():
    db_name = request.form.get('db', DEFAULT_DB)
    group = request.form.get('group')
    index = request.form.get('index')
    
    if not group or index is None:
        return jsonify({"success": False, "error": "Missing group or index"})
    
    try:
        index = int(index)
        data = load_data(db_name)
        
        if group not in data or index < 0 or index >= len(data[group]):
            return jsonify({"success": False, "error": "Website not found"})
        
        # 获取当前网站信息
        website = data[group][index]
        
        # 更新信息
        website['title'] = request.form.get('title')
        website['url'] = request.form.get('url')
        website['note'] = request.form.get('note', '')
        
        # 处理图片
        if 'image' in request.files and request.files['image'].filename:
            image = request.files['image']
            filename = f"{int(datetime.now().timestamp() * 1000)}.png"
            img_folder = f'img/{db_name}'
            os.makedirs(f'static/{img_folder}', exist_ok=True)
            image_path = os.path.join(app.config['UPLOAD_FOLDER'], db_name, filename)
            image.save(image_path)
            
            # 如果原图不是默认图片，则删除
            if website['img'] != 'img/default-icon.png' and os.path.exists(f"static/{website['img']}"):
                os.remove(f"static/{website['img']}")
            
            website['img'] = f"{img_folder}/{filename}"
        elif request.form.get('image_data'):
            # 处理Base64图片数据
            image_data = request.form.get('image_data').split(',')[1]
            filename = f"{int(datetime.now().timestamp() * 1000)}.png"
            img_folder = f'img/{db_name}'
            os.makedirs(f'static/{img_folder}', exist_ok=True)
            image_path = os.path.join(app.config['UPLOAD_FOLDER'], db_name, filename)
            
            with open(image_path, "wb") as f:
                f.write(base64.b64decode(image_data))
            
            # 如果原图不是默认图片，则删除
            if website['img'] != 'img/default-icon.png' and os.path.exists(f"static/{website['img']}"):
                os.remove(f"static/{website['img']}")
            
            website['img'] = f"{img_folder}/{filename}"
        
        save_data(data, db_name)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/delete-group', methods=['POST'])
def delete_group():
    db_name = request.json.get('db', DEFAULT_DB)
    group = request.json.get('group')
    
    if not group:
        return jsonify({"success": False, "error": "Missing group"})
    
    data = load_data(db_name)
    
    if group in data:
        del data[group]
        save_data(data, db_name)
        return jsonify({"success": True})
    
    return jsonify({"success": False, "error": "Group not found"})

if __name__ == '__main__':
    ensure_files_exist()
    app.run(host='0.0.0.0', port=4157, debug=True)

