// 创建右键菜单
chrome.runtime.onInstalled.addListener(() => {
  // 页面上下文菜单
  chrome.contextMenus.create({
    id: "add-page",
    title: "添加当前页面到导航",
    contexts: ["page"],
  })

  // 链接上下文菜单
  chrome.contextMenus.create({
    id: "add-link",
    title: "添加链接到导航",
    contexts: ["link"],
  })

  // 图片上下文菜单
  chrome.contextMenus.create({
    id: "add-image",
    title: "添加图片到导航",
    contexts: ["image"],
  })

  // 选中文本上下文菜单
  chrome.contextMenus.create({
    id: "add-selection",
    title: "添加选中文本到导航",
    contexts: ["selection"],
  })
})

// 将图片URL转换为base64
async function getImageAsBase64(imgUrl) {
  try {
    // 获取图片数据
    const response = await fetch(imgUrl, { mode: "no-cors" })
    const blob = await response.blob()

    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onloadend = () => resolve(reader.result)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  } catch (error) {
    console.error("获取图片数据失败:", error)
    return null
  }
}

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  let contextData = {}

  switch (info.menuItemId) {
    case "add-page":
      contextData = {
        title: tab.title,
        url: tab.url,
        img: tab.favIconUrl,
      }

      // 使用注入的弹出窗口
      chrome.tabs.sendMessage(tab.id, {
        action: "showInjectedPopup",
        data: contextData,
      })
      return

    case "add-link":
      contextData = {
        title: info.linkText || "链接",
        url: info.linkUrl,
        img: null,
      }

      // 使用注入的弹出窗口
      chrome.tabs.sendMessage(tab.id, {
        action: "showInjectedPopup",
        data: contextData,
      })
      return

    case "add-image":
      try {
        // 获取图片的base64数据
        const imgBase64 = await getImageAsBase64(info.srcUrl)

        // 获取图片所在页面的信息
        contextData = {
          title: tab.title,
          url: tab.url,
          img: imgBase64 || info.srcUrl, // 如果获取base64失败，则使用原始URL
          note: `图片来源: ${tab.url}`,
        }

        // 使用注入的弹出窗口
        chrome.tabs.sendMessage(tab.id, {
          action: "showInjectedPopup",
          data: contextData,
        })
      } catch (error) {
        console.error("处理图片失败:", error)

        // 如果获取base64失败，仍然尝试使用原始URL
        contextData = {
          title: tab.title,
          url: tab.url,
          img: info.srcUrl,
          note: `图片来源: ${tab.url}`,
        }

        chrome.tabs.sendMessage(tab.id, {
          action: "showInjectedPopup",
          data: contextData,
        })
      }
      return

    case "add-selection":
      // 直接使用选中的文本
      contextData = {
        title: tab.title,
        url: tab.url,
        img: tab.favIconUrl,
        note: info.selectionText || "",
      }

      // 使用注入的弹出窗口
      chrome.tabs.sendMessage(tab.id, {
        action: "showInjectedPopup",
        data: contextData,
      })
      return
  }
})

