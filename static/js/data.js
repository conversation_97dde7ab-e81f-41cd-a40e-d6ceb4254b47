// 声明全局变量
const loading = document.getElementById("loading") // 假设loading元素在HTML中存在
const addWebsiteModal = document.getElementById("addWebsiteModal") // 假设addWebsiteModal元素在HTML中存在
let activeGroup = new URLSearchParams(window.location.search).get("group") || "default" // 默认分组为'default'
const currentDb = new URLSearchParams(window.location.search).get("db") || "default_db" // 默认数据库为'default_db'
const addGroupModal = document.getElementById("addGroupModal") // 假设addGroupModal元素在HTML中存在
const groupsList = document.getElementById("groupsList") // 假设groupsList元素在HTML中存在
const websiteGroupSelect = document.getElementById("website-group") // 假设websiteGroupSelect元素在HTML中存在
const addWebsiteForm = document.getElementById("addWebsiteForm") // 假设addWebsiteForm元素在HTML中存在
const addDbModal = document.getElementById("addDbModal") // 假设addDbModal元素在HTML中存在
const websitesContainer = document.getElementById("websites") // 假设websitesContainer元素在HTML中存在

// 声明未声明的变量
let currentImages = []
const selectedCards = new Set()
const isManageMode = false

// 处理添加网站表单提交
function handleAddWebsiteSubmit(e) {
  e.preventDefault()

  const formData = new FormData(this)

  loading.style.display = "flex"

  fetch("/api/add", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        addWebsiteModal.style.display = "none"
        // 重新加载当前分组
        loadGroupWebsites(formData.get("group"))
        // 如果当前不是该分组，切换到该分组
        if (activeGroup !== formData.get("group")) {
          activeGroup = formData.get("group")
          // 更新活动分组UI
          document.querySelectorAll(".group-item").forEach((item) => {
            item.classList.remove("active")
            if (item.dataset.group === activeGroup) {
              item.classList.add("active")
            }
          })
          // 更新URL参数
          const url = new URL(window.location)
          url.searchParams.set("group", activeGroup)
          window.history.pushState({}, "", url)
        }
      } else {
        alert("添加网站失败")
      }
    })
    .catch((error) => {
      console.error("添加网站出错:", error)
      loading.style.display = "none"
      alert("添加网站时出错")
    })
}

// 处理添加分组表单提交
function handleAddGroupSubmit(e) {
  e.preventDefault()

  const groupName = document.getElementById("group-name").value.trim()
  if (!groupName) return

  loading.style.display = "flex"

  fetch("/api/add-group", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name: groupName, db: currentDb }),
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        addGroupModal.style.display = "none"

        // 添加新分组到列表
        const newGroupItem = document.createElement("li")
        newGroupItem.className = "group-item"
        newGroupItem.dataset.group = groupName
        newGroupItem.innerHTML = `
                <span class="group-name">${groupName}</span>
                <span class="group-count" id="group-count-${groupName}">0</span>
            `

        groupsList.appendChild(newGroupItem)

        // 添加到下拉列表
        const option = document.createElement("option")
        option.value = groupName
        option.textContent = groupName
        websiteGroupSelect.appendChild(option)

        // 如果是从添加网站模态框打开的，则返回到添加网站
        if (addWebsiteForm.style.display !== "none") {
          addWebsiteModal.style.display = "block"
          websiteGroupSelect.value = groupName
        }
      } else {
        alert(data.error || "添加分组失败")
      }
    })
    .catch((error) => {
      console.error("添加分组出错:", error)
      loading.style.display = "none"
      alert("添加分组时出错")
    })
}

// 处理添加数据库表单提交
function handleAddDbSubmit(e) {
  e.preventDefault()

  const dbName = document.getElementById("db-name").value.trim()
  if (!dbName) return

  loading.style.display = "flex"

  fetch("/api/create-database", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name: dbName }),
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        addDbModal.style.display = "none"
        // 跳转到新数据库
        window.location.href = `/?db=${dbName}`
      } else {
        alert(data.error || "创建数据库失败")
      }
    })
    .catch((error) => {
      console.error("创建数据库出错:", error)
      loading.style.display = "none"
      alert("创建数据库时出错")
    })
}

// 加载分组网站
function loadGroupWebsites(groupName) {
  loading.style.display = "flex"
  websitesContainer.innerHTML = ""

  fetch(`/api/group/${encodeURIComponent(groupName)}?db=${currentDb}`)
    .then((response) => response.json())
    .then((websites) => {
      loading.style.display = "none"

      if (websites.length === 0) {
        websitesContainer.innerHTML = '<div class="no-results">该分组没有网站</div>'
      } else {
        // 更新当前图片数组
        currentImages = websites.map((website) => ({
          src: `/static/${website.img}`,
          title: website.title,
        }))

        websites.forEach((website, index) => {
          websitesContainer.appendChild(createWebsiteCard(website, groupName, index))
        })

        // 更新分组计数
        updateGroupCount(groupName, websites.length)

        // 实现懒加载
        implementLazyLoading()
      }
    })
    .catch((error) => {
      console.error("加载网站出错:", error)
      loading.style.display = "none"
      websitesContainer.innerHTML = '<div class="no-results">加载网站时出错</div>'
    })
}

// 更新分组计数
function updateGroupCount(groupName, count) {
  const countElement = document.getElementById(`group-count-${groupName}`)
  if (countElement) {
    countElement.textContent = count.toString()
  }
}

// 删除网站
function deleteWebsites(group, indices) {
  loading.style.display = "flex"

  fetch("/api/delete", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ db: currentDb, group, indices }),
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        // 清除选中
        selectedCards.clear()
        updateSelectedCount()

        // 重新加载当前分组
        loadGroupWebsites(activeGroup)
      } else {
        alert(data.error || "删除网站失败")
      }
    })
    .catch((error) => {
      console.error("删除网站出错:", error)
      loading.style.display = "none"
      alert("删除网站时出错")
    })
}

// 保存备注
function saveNote(group, index, note) {
  loading.style.display = "flex"

  fetch("/api/edit", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      db: currentDb,
      group: group,
      index: index,
      note: note,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        // 关闭模态框
        document.getElementById("note-viewer-modal").style.display = "none"
        // 重新加载当前分组
        loadGroupWebsites(activeGroup)
      } else {
        alert(data.error || "保存备注失败")
      }
    })
    .catch((error) => {
      console.error("保存备注出错:", error)
      loading.style.display = "none"
      alert("保存备注时出错")
    })
}

// 移动网站
function moveWebsites(fromGroup, toGroup, indices) {
  loading.style.display = "flex"

  fetch("/api/move", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      db: currentDb,
      fromGroup,
      toGroup,
      indices,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        // 清除选中
        selectedCards.clear()
        updateSelectedCount()

        // 重新加载当前分组
        loadGroupWebsites(activeGroup)
      } else {
        alert(data.error || "移动网站失败")
      }
    })
    .catch((error) => {
      console.error("移动网站出错:", error)
      loading.style.display = "none"
      alert("移动网站时出错")
    })
}

// 实现懒加载
function implementLazyLoading() {
  const cards = document.querySelectorAll(".website-card")

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const card = entry.target
          const img = card.querySelector('img[loading="lazy"]')
          if (img) {
            img.src = img.getAttribute("src")
            img.removeAttribute("loading")
          }
          observer.unobserve(card)
        }
      })
    },
    {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    },
  )

  cards.forEach((card) => {
    observer.observe(card)
  })
}

// 初始化分组拖拽功能
function initGroupDragDrop() {
  document.querySelectorAll(".group-item").forEach((group) => {
    group.addEventListener("dragover", function (e) {
      if (!isManageMode) return
      e.preventDefault()
      this.classList.add("drag-over")
    })

    group.addEventListener("dragleave", function () {
      this.classList.remove("drag-over")
    })

    group.addEventListener("drop", function (e) {
      if (!isManageMode) return
      e.preventDefault()
      this.classList.remove("drag-over")

      try {
        const data = JSON.parse(e.dataTransfer.getData("text/plain"))
        const fromGroup = data.group
        const toGroup = this.dataset.group
        const indices = data.indices

        if (fromGroup === toGroup || !indices.length) return

        // 移动网站到新分组
        moveWebsites(fromGroup, toGroup, indices)
      } catch (error) {
        console.error("拖放处理出错:", error)
      }
    })
  })
}

// 在DOM加载完成后初始化分组拖拽功能
document.addEventListener("DOMContentLoaded", () => {
  // 初始化分组拖拽功能
  initGroupDragDrop()
})

// 占位函数，需要根据实际情况进行实现
function createWebsiteCard(website, groupName, index) {
  // 这里应该返回一个表示网站的DOM元素
  const card = document.createElement("div")
  card.classList.add("website-card")
  card.innerHTML = `<h3>${website.title}</h3><p>${website.url}</p>` // 示例内容
  return card
}

function updateSelectedCount() {
  // 更新选中计数
  console.log("updateSelectedCount called")
}

