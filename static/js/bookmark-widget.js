/**
 * 导航收藏小工具 - 可植入任何网页的收藏按钮
 * 使用方法：在网页中引入此JS文件即可
 */

(function() {
    'use strict';
    
    // 配置
    const CONFIG = {
        API_BASE: 'http://localhost:4157',
        BUTTON_SIZE: '50px',
        BUTTON_COLOR: '#4a6bff',
        BUTTON_HOVER_COLOR: '#3a5bef',
        Z_INDEX: 999999
    };
    
    // 防止重复加载
    if (window.bookmarkWidgetLoaded) {
        return;
    }
    window.bookmarkWidgetLoaded = true;
    
    // 创建样式
    function createStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .bookmark-widget {
                position: fixed;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                z-index: ${CONFIG.Z_INDEX};
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .bookmark-button {
                width: ${CONFIG.BUTTON_SIZE};
                height: ${CONFIG.BUTTON_SIZE};
                background: ${CONFIG.BUTTON_COLOR};
                border: none;
                border-radius: 8px 0 0 8px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                transition: all 0.3s ease;
                transform: translateX(70%);
                box-shadow: -2px 2px 8px rgba(0,0,0,0.2);
            }
            
            .bookmark-button:hover {
                background: ${CONFIG.BUTTON_HOVER_COLOR};
                transform: translateX(0);
            }
            
            .bookmark-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: ${CONFIG.Z_INDEX + 1};
                display: none;
                align-items: center;
                justify-content: center;
            }
            
            .bookmark-modal-content {
                background: white;
                border-radius: 8px;
                padding: 24px;
                width: 90%;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                animation: modalSlideIn 0.3s ease;
            }
            
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.9) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }
            
            .bookmark-modal h3 {
                margin: 0 0 16px 0;
                color: #333;
                font-size: 18px;
            }
            
            .bookmark-form-group {
                margin-bottom: 16px;
            }
            
            .bookmark-form-group label {
                display: block;
                margin-bottom: 4px;
                color: #555;
                font-size: 14px;
                font-weight: 500;
            }
            
            .bookmark-form-group input,
            .bookmark-form-group select,
            .bookmark-form-group textarea {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                box-sizing: border-box;
            }
            
            .bookmark-form-group textarea {
                height: 60px;
                resize: vertical;
            }
            
            .bookmark-form-actions {
                display: flex;
                gap: 8px;
                justify-content: flex-end;
                margin-top: 20px;
            }
            
            .bookmark-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s ease;
            }
            
            .bookmark-btn-primary {
                background: ${CONFIG.BUTTON_COLOR};
                color: white;
            }
            
            .bookmark-btn-primary:hover {
                background: ${CONFIG.BUTTON_HOVER_COLOR};
            }
            
            .bookmark-btn-secondary {
                background: #f5f5f5;
                color: #666;
            }
            
            .bookmark-btn-secondary:hover {
                background: #e8e8e8;
            }
            
            .bookmark-status {
                padding: 8px 12px;
                border-radius: 4px;
                margin-bottom: 16px;
                font-size: 14px;
            }
            
            .bookmark-status.success {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }
            
            .bookmark-status.error {
                background: #fff2f0;
                color: #ff4d4f;
                border: 1px solid #ffccc7;
            }
            
            .bookmark-loading {
                display: inline-block;
                width: 16px;
                height: 16px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid ${CONFIG.BUTTON_COLOR};
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-right: 8px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    
    // 创建HTML结构
    function createHTML() {
        const widget = document.createElement('div');
        widget.className = 'bookmark-widget';
        widget.innerHTML = `
            <button class="bookmark-button" title="收藏到导航">
                ⭐
            </button>
            
            <div class="bookmark-modal">
                <div class="bookmark-modal-content">
                    <h3>收藏网页</h3>
                    <div class="bookmark-status" style="display: none;"></div>
                    <form class="bookmark-form">
                        <div class="bookmark-form-group">
                            <label for="bookmark-title">网站名称</label>
                            <input type="text" id="bookmark-title" required>
                        </div>
                        <div class="bookmark-form-group">
                            <label for="bookmark-url">网站地址</label>
                            <input type="url" id="bookmark-url" required>
                        </div>
                        <div class="bookmark-form-group">
                            <label for="bookmark-db">数据库</label>
                            <select id="bookmark-db">
                                <option value="data">默认</option>
                            </select>
                        </div>
                        <div class="bookmark-form-group">
                            <label for="bookmark-group">分组</label>
                            <div style="display: flex; gap: 8px;">
                                <select id="bookmark-group" style="flex: 1;">
                                    <option value="工具">工具</option>
                                </select>
                                <button type="button" class="bookmark-btn bookmark-btn-secondary" id="new-group-btn">新建</button>
                            </div>
                        </div>
                        <div class="bookmark-form-group">
                            <label for="bookmark-note">备注</label>
                            <textarea id="bookmark-note" placeholder="可选"></textarea>
                        </div>
                        <div class="bookmark-form-actions">
                            <button type="button" class="bookmark-btn bookmark-btn-secondary" id="bookmark-cancel">取消</button>
                            <button type="submit" class="bookmark-btn bookmark-btn-primary" id="bookmark-submit">
                                <span class="submit-text">保存</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        
        document.body.appendChild(widget);
        return widget;
    }

    // 获取网页图标
    function getPageIcon() {
        // 尝试获取favicon
        const favicon = document.querySelector('link[rel="icon"], link[rel="shortcut icon"]');
        if (favicon) {
            return favicon.href;
        }

        // 尝试获取apple-touch-icon
        const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]');
        if (appleTouchIcon) {
            return appleTouchIcon.href;
        }

        // 默认favicon路径
        const defaultFavicon = `${window.location.protocol}//${window.location.host}/favicon.ico`;
        return defaultFavicon;
    }

    // 显示状态消息
    function showStatus(message, type = 'success') {
        const statusEl = document.querySelector('.bookmark-status');
        statusEl.textContent = message;
        statusEl.className = `bookmark-status ${type}`;
        statusEl.style.display = 'block';

        if (type === 'success') {
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }
    }

    // 设置加载状态
    function setLoading(isLoading) {
        const submitBtn = document.getElementById('bookmark-submit');
        const submitText = submitBtn.querySelector('.submit-text');

        if (isLoading) {
            submitBtn.disabled = true;
            submitText.innerHTML = '<span class="bookmark-loading"></span>保存中...';
        } else {
            submitBtn.disabled = false;
            submitText.textContent = '保存';
        }
    }

    // 加载数据库列表
    function loadDatabases() {
        fetch(`${CONFIG.API_BASE}/api/databases`)
            .then(response => response.json())
            .then(databases => {
                const dbSelect = document.getElementById('bookmark-db');
                dbSelect.innerHTML = '';

                databases.forEach(db => {
                    const option = document.createElement('option');
                    option.value = db;
                    option.textContent = db === 'data' ? '默认' : db;
                    dbSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('加载数据库列表失败:', error);
            });
    }

    // 加载分组列表
    function loadGroups(db = 'data') {
        fetch(`${CONFIG.API_BASE}/api/groups?db=${db}`)
            .then(response => response.json())
            .then(groups => {
                const groupSelect = document.getElementById('bookmark-group');
                groupSelect.innerHTML = '';

                if (groups.length === 0) {
                    const option = document.createElement('option');
                    option.value = '工具';
                    option.textContent = '工具';
                    groupSelect.appendChild(option);
                } else {
                    groups.forEach(group => {
                        const option = document.createElement('option');
                        option.value = group;
                        option.textContent = group;
                        groupSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('加载分组列表失败:', error);
                showStatus('无法连接到导航系统，请确保服务已启动', 'error');
            });
    }

    // 创建新分组
    function createNewGroup() {
        const groupName = prompt('请输入新分组名称:');
        if (!groupName || groupName.trim() === '') return;

        const db = document.getElementById('bookmark-db').value;

        fetch(`${CONFIG.API_BASE}/api/add-group`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: groupName.trim(),
                db: db
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 重新加载分组列表
                loadGroups(db);
                // 选中新创建的分组
                setTimeout(() => {
                    document.getElementById('bookmark-group').value = groupName.trim();
                }, 100);
                showStatus('分组创建成功');
            } else {
                showStatus(data.error || '创建分组失败', 'error');
            }
        })
        .catch(error => {
            console.error('创建分组失败:', error);
            showStatus('创建分组时出错', 'error');
        });
    }

    // 保存收藏
    function saveBookmark(formData) {
        setLoading(true);

        const data = {
            title: formData.get('title'),
            url: formData.get('url'),
            group: formData.get('group'),
            note: formData.get('note') || '',
            img: getPageIcon(),
            db: formData.get('db')
        };

        fetch(`${CONFIG.API_BASE}/api/extension/add`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
        })
        .then(response => response.json())
        .then(result => {
            setLoading(false);
            if (result.success) {
                showStatus('收藏成功！');
                setTimeout(() => {
                    closeModal();
                }, 1500);
            } else {
                showStatus(result.error || '收藏失败', 'error');
            }
        })
        .catch(error => {
            console.error('收藏失败:', error);
            setLoading(false);
            showStatus('收藏时出错，请检查网络连接', 'error');
        });
    }

    // 打开模态框
    function openModal() {
        const modal = document.querySelector('.bookmark-modal');
        modal.style.display = 'flex';

        // 填充当前页面信息
        document.getElementById('bookmark-title').value = document.title;
        document.getElementById('bookmark-url').value = window.location.href;

        // 清空状态和备注
        document.querySelector('.bookmark-status').style.display = 'none';
        document.getElementById('bookmark-note').value = '';

        // 加载数据
        loadDatabases();
        loadGroups();
    }

    // 关闭模态框
    function closeModal() {
        const modal = document.querySelector('.bookmark-modal');
        modal.style.display = 'none';

        // 重置表单
        document.querySelector('.bookmark-form').reset();
        setLoading(false);
    }

    // 绑定事件
    function bindEvents(widget) {
        const button = widget.querySelector('.bookmark-button');
        const modal = widget.querySelector('.bookmark-modal');
        const form = widget.querySelector('.bookmark-form');
        const cancelBtn = widget.querySelector('#bookmark-cancel');
        const newGroupBtn = widget.querySelector('#new-group-btn');
        const dbSelect = widget.querySelector('#bookmark-db');

        // 按钮点击事件
        button.addEventListener('click', openModal);

        // 取消按钮
        cancelBtn.addEventListener('click', closeModal);

        // 点击模态框背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeModal();
            }
        });

        // 新建分组按钮
        newGroupBtn.addEventListener('click', createNewGroup);

        // 数据库切换事件
        dbSelect.addEventListener('change', (e) => {
            loadGroups(e.target.value);
        });

        // 表单提交
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(form);

            // 设置数据库值
            formData.set('db', dbSelect.value);
            formData.set('group', document.getElementById('bookmark-group').value);

            saveBookmark(formData);
        });
    }

    // 初始化
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 创建样式和HTML
        createStyles();
        const widget = createHTML();
        bindEvents(widget);

        console.log('导航收藏小工具已加载');
    }

    // 启动
    init();

})();
