// 备注预览和编辑功能
document.addEventListener("DOMContentLoaded", () => {
  // 创建备注预览/编辑模态框
  createNoteModal()

  // 监听备注点击事件
  document.addEventListener("click", (e) => {
    const noteElement = e.target.closest(".card-note")
    if (noteElement) {
      e.preventDefault()
      openNoteEditor(noteElement)
    }
  })

  // 初始化Markdown工具栏事件
  initMarkdownToolbar()
})

// 创建备注编辑模态框
function createNoteModal() {
  const modalHtml = `
    <div id="note-modal" class="modal">
      <div class="modal-content note-modal-content">
        <span class="close">&times;</span>
        <h2>备注编辑</h2>
        <div class="note-editor-container">
          <div class="markdown-toolbar">
            <button type="button" class="md-tool" data-action="bold" title="粗体">
              <i class="fas fa-bold"></i>
            </button>
            <button type="button" class="md-tool" data-action="italic" title="斜体">
              <i class="fas fa-italic"></i>
            </button>
            <button type="button" class="md-tool" data-action="heading" title="标题">
              <i class="fas fa-heading"></i>
            </button>
            <button type="button" class="md-tool" data-action="link" title="链接">
              <i class="fas fa-link"></i>
            </button>
            <button type="button" class="md-tool" data-action="list" title="列表">
              <i class="fas fa-list-ul"></i>
            </button>
            <button type="button" class="md-tool" data-action="code" title="代码">
              <i class="fas fa-code"></i>
            </button>
            <button type="button" class="md-tool" data-action="preview" title="编辑">
              <i class="fas fa-edit"></i>
            </button>
          </div>
          <div class="note-editor-wrapper">
            <textarea id="note-editor" class="note-editor" style="display: none;"></textarea>
            <div id="note-preview" class="note-preview markdown-body"></div>
          </div>
          <input type="hidden" id="note-website-index">
          <input type="hidden" id="note-website-group">
        </div>
        <div class="form-actions">
          <button type="button" class="btn primary" id="save-note-btn">保存</button>
          <button type="button" class="btn cancel" id="cancel-note-btn">取消</button>
        </div>
      </div>
    </div>
  `

  document.body.insertAdjacentHTML("beforeend", modalHtml)

  // 添加关闭事件
  document.querySelector("#note-modal .close").addEventListener("click", () => {
    document.getElementById("note-modal").style.display = "none"
  })

  document.getElementById("cancel-note-btn").addEventListener("click", () => {
    document.getElementById("note-modal").style.display = "none"
  })

  // 点击模态框外部关闭
  window.addEventListener("click", (e) => {
    if (e.target === document.getElementById("note-modal")) {
      document.getElementById("note-modal").style.display = "none"
    }
  })

  // 保存备注
  document.getElementById("save-note-btn").addEventListener("click", saveNote)
}

// 打开备注编辑器
function openNoteEditor(noteElement) {
  const card = noteElement.closest(".website-card")
  const index = card.dataset.index
  const group = card.dataset.group
  const db = getCurrentDb()

  // 获取网站数据
  fetch(`/api/website?db=${db}&group=${encodeURIComponent(group)}&index=${index}`)
    .then((response) => response.json())
    .then((website) => {
      if (website) {
        const noteEditor = document.getElementById("note-editor")
        const notePreview = document.getElementById("note-preview")
        const noteModal = document.getElementById("note-modal")

        // 设置备注内容
        noteEditor.value = website.note || ""

        // 设置网站索引和分组
        document.getElementById("note-website-index").value = index
        document.getElementById("note-website-group").value = group

        // 默认为预览模式
        noteEditor.style.display = "none"
        notePreview.style.display = "block"
        notePreview.innerHTML = markdownToHtml(noteEditor.value)
        document.querySelector('[data-action="preview"]').innerHTML = '<i class="fas fa-edit"></i>'

        // 显示模态框
        noteModal.style.display = "block"
      }
    })
    .catch((error) => {
      console.error("获取网站信息出错:", error)
      alert("获取网站信息时出错")
    })
}

// 保存备注
function saveNote() {
  const index = document.getElementById("note-website-index").value
  const group = document.getElementById("note-website-group").value
  const note = document.getElementById("note-editor").value
  const db = getCurrentDb()

  // 显示加载动画
  const loading = document.getElementById("loading")
  loading.style.display = "flex"

  // 构建表单数据
  const formData = new FormData()
  formData.append("db", db)
  formData.append("group", group)
  formData.append("index", index)
  formData.append("note", note)

  // 获取当前网站数据以保留其他字段
  fetch(`/api/website?db=${db}&group=${encodeURIComponent(group)}&index=${index}`)
    .then((response) => response.json())
    .then((website) => {
      if (website) {
        formData.append("title", website.title)
        formData.append("url", website.url)

        // 发送更新请求
        return fetch("/api/edit", {
          method: "POST",
          body: formData,
        })
      }
      throw new Error("Website not found")
    })
    .then((response) => response.json())
    .then((data) => {
      loading.style.display = "none"
      if (data.success) {
        document.getElementById("note-modal").style.display = "none"

        // 更新卡片上的备注
        const card = document.querySelector(`.website-card[data-index="${index}"][data-group="${group}"]`)
        if (card) {
          const noteElement = card.querySelector(".card-note")
          if (noteElement) {
            noteElement.textContent = note
          } else if (note) {
            // 如果之前没有备注元素，但现在有备注了，添加备注元素
            const cardContent = card.querySelector(".card-content")
            const cardActions = card.querySelector(".card-actions")
            const noteDiv = document.createElement("p")
            noteDiv.className = "card-note"
            noteDiv.textContent = note
            cardContent.insertBefore(noteDiv, cardActions)
          }
        }
      } else {
        alert(data.error || "保存备注失败")
      }
    })
    .catch((error) => {
      console.error("保存备注出错:", error)
      loading.style.display = "none"
      alert("保存备注时出错")
    })
}

// 初始化Markdown工具栏
function initMarkdownToolbar() {
  document.querySelectorAll(".md-tool").forEach((button) => {
    button.addEventListener("click", function () {
      const action = this.dataset.action
      const noteEditor = document.getElementById("note-editor")
      const notePreview = document.getElementById("note-preview")

      // 获取选中的文本
      const start = noteEditor.selectionStart
      const end = noteEditor.selectionEnd
      const selectedText = noteEditor.value.substring(start, end)

      let replacement = ""
      let cursorOffset = 0

      switch (action) {
        case "bold":
          replacement = `**${selectedText}**`
          cursorOffset = 2
          break
        case "italic":
          replacement = `*${selectedText}*`
          cursorOffset = 1
          break
        case "heading":
          replacement = `## ${selectedText}`
          cursorOffset = 3
          break
        case "link":
          replacement = `[${selectedText}](url)`
          cursorOffset = 3
          break
        case "list":
          replacement = `- ${selectedText}`
          cursorOffset = 2
          break
        case "code":
          replacement = `\`${selectedText}\``
          cursorOffset = 1
          break
        case "preview":
          // 切换预览模式
          if (noteEditor.style.display !== "none") {
            // 切换到预览模式
            notePreview.innerHTML = markdownToHtml(noteEditor.value)
            noteEditor.style.display = "none"
            notePreview.style.display = "block"
            this.innerHTML = '<i class="fas fa-edit"></i>'
          } else {
            // 切换回编辑模式
            noteEditor.style.display = "block"
            notePreview.style.display = "none"
            this.innerHTML = '<i class="fas fa-eye"></i>'
          }
          return
      }

      // 插入Markdown标记
      if (action !== "preview") {
        noteEditor.focus()
        noteEditor.setRangeText(replacement, start, end, "end")

        // 如果没有选中文本，将光标放在标记中间
        if (start === end) {
          noteEditor.setSelectionRange(start + cursorOffset, start + cursorOffset)
        }
      }
    })
  })
}

// 简单的Markdown转HTML函数
function markdownToHtml(markdown) {
  if (!markdown) return ""

  // 替换标题
  let html = markdown.replace(/^(#{1,6})\s+(.*?)$/gm, (match, hashes, content) => {
    const level = hashes.length
    return `<h${level}>${content}</h${level}>`
  })

  // 替换粗体
  html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")

  // 替换斜体
  html = html.replace(/\*(.*?)\*/g, "<em>$1</em>")

  // 替换链接
  html = html.replace(/\[(.*?)\]$$(.*?)$$/g, '<a href="$2" target="_blank">$1</a>')

  // 替换列表
  html = html.replace(/^-\s+(.*?)$/gm, "<li>$1</li>")
  html = html.replace(/<li>.*?<\/li>/gs, "<ul>$&</ul>")

  // 替换代码
  html = html.replace(/`(.*?)`/g, "<code>$1</code>")

  // 替换段落
  html = html.replace(/^(?!<[a-z]|$)(.+)$/gm, "<p>$1</p>")

  return html
}

// 获取当前数据库
function getCurrentDb() {
  return document.getElementById("current-db")?.querySelector("span")?.textContent || "data"
}

// 添加CSS样式
function addStyles() {
  const style = document.createElement("style")
  style.textContent = `
    .note-modal-content {
      width: 80%;
      height: 80%;
      max-width: none;
      display: flex;
      flex-direction: column;
    }
    
    .note-editor-container {
      margin-bottom: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .markdown-toolbar {
      display: flex;
      gap: 5px;
      margin-bottom: 10px;
      padding: 5px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    .md-tool {
      background: none;
      border: none;
      padding: 5px 8px;
      cursor: pointer;
      border-radius: 3px;
    }
    
    .md-tool:hover {
      background-color: #e0e0e0;
    }
    
    .note-editor-wrapper {
      position: relative;
      flex: 1;
      overflow: hidden;
      display: flex;
    }
    
    .note-editor, .note-preview {
      width: 100%;
      height: 100%;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.5;
      overflow-y: auto;
    }
    
    .note-preview {
      background-color: #f9f9f9;
    }
    
    .note-preview h1, .note-preview h2, .note-preview h3, 
    .note-preview h4, .note-preview h5, .note-preview h6 {
      margin-top: 16px;
      margin-bottom: 8px;
    }
    
    .note-preview p {
      margin-bottom: 8px;
    }
    
    .note-preview ul {
      padding-left: 20px;
      margin-bottom: 8px;
    }
    
    .note-preview code {
      background-color: #f0f0f0;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
    }
    
    .card-note {
      cursor: pointer;
    }
    
    .card-note:hover {
      text-decoration: underline;
      color: var(--primary-color);
    }
    
    /* 浮动图片按钮样式 */
    .nav-add-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(74, 107, 255, 0.8);
      color: white;
      border: none;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .nav-add-btn:hover {
      background-color: rgba(74, 107, 255, 1);
    }
    
    .nav-image-container {
      position: relative;
      display: inline-block;
    }
    
    .nav-image-container:hover .nav-add-btn {
      opacity: 1;
    }
  `

  document.head.appendChild(style)
}

// 页面加载完成后添加样式
document.addEventListener("DOMContentLoaded", addStyles)

