document.addEventListener("DOMContentLoaded", () => {
    // 元素引用
    const websitesContainer = document.getElementById("websites-container")
    const searchResults = document.getElementById("search-results")
    const groupsList = document.getElementById("groups-list")
    const searchInput = document.getElementById("search-input")
    const searchBtn = document.getElementById("search-btn")
    const addWebsiteBtn = document.getElementById("add-website-btn")
    const manageBtn = document.getElementById("manage-btn")
    const settingsBtn = document.getElementById("settings-btn")
    const addWebsiteModal = document.getElementById("add-website-modal")
    const addGroupModal = document.getElementById("add-group-modal")
    const addDbModal = document.getElementById("add-db-modal")
    const cleanImagesModal = document.getElementById("clean-images-modal")
    const imageViewerModal = document.getElementById("image-viewer-modal")
    const settingsModal = document.getElementById("settings-modal")
    const addWebsiteForm = document.getElementById("add-website-form")
    const addGroupForm = document.getElementById("add-group-form")
    const addDbForm = document.getElementById("add-db-form")
    const websiteGroupSelect = document.getElementById("website-group")
    const newGroupBtn = document.getElementById("new-group-btn")
    const websiteImage = document.getElementById("website-image")
    const imagePreview = document.getElementById("image-preview")
    const pasteImageBtn = document.getElementById("paste-image-btn")
    const imageData = document.getElementById("image-data")
    const loading = document.getElementById("loading")
    const managementToolbar = document.getElementById("management-toolbar")
    const selectedCountSpan = document.getElementById("selected-count")
    const cancelSelectionBtn = document.getElementById("cancel-selection-btn")
    const deleteSelectedBtn = document.getElementById("delete-selected-btn")
    const currentDbElement = document.getElementById("current-db")
    const dbDropdown = document.getElementById("db-dropdown")
    const addGroupBtn = document.getElementById("add-group-btn")

    // 移动端菜单元素
    const mobileMenuToggle = document.getElementById("mobile-menu-toggle")
    const sidebar = document.getElementById("sidebar")
    const sidebarOverlay = document.getElementById("sidebar-overlay")

    // 图片查看器元素
    const viewerImage = document.getElementById("viewer-image")
    const prevImageBtn = document.getElementById("prev-image-btn")
    const nextImageBtn = document.getElementById("next-image-btn")
    const flipHBtn = document.getElementById("flip-h-btn")
    const flipVBtn = document.getElementById("flip-v-btn")
    const zoomInBtn = document.getElementById("zoom-in-btn")
    const zoomOutBtn = document.getElementById("zoom-out-btn")
    const zoomResetBtn = document.getElementById("zoom-reset-btn")
    const imageViewerTitle = document.getElementById("image-viewer-title")
  
    // 状态变量
    let activeGroup = document.querySelector(".group-item.active")?.dataset.group
    const currentDb = document.getElementById("website-db").value
    let isManageMode = false
    const selectedCards = new Set()
    let currentImages = []
    let currentImageIndex = 0
    let imageScale = 1
    let imageFlipH = false
    let imageFlipV = false
  
    // 加载当前分组的网站
    if (activeGroup) {
      loadGroupWebsites(activeGroup)
    }
  
    // 分组点击事件
    groupsList.addEventListener("click", (e) => {
      const groupItem = e.target.closest(".group-item")
      if (!groupItem) return

      const newGroup = groupItem.dataset.group
      if (newGroup !== activeGroup) {
        switchToGroup(newGroup)
      }
    })

    // 切换到指定分组的函数
    function switchToGroup(groupName) {
      // 切换分组
      document.querySelectorAll(".group-item").forEach((item) => {
        item.classList.remove("active")
      })

      const targetGroupItem = document.querySelector(`[data-group="${groupName}"]`)
      if (targetGroupItem) {
        targetGroupItem.classList.add("active")
      }

      activeGroup = groupName
      loadGroupWebsites(activeGroup)

      // 更新URL参数
      const url = new URL(window.location)
      url.searchParams.set("group", activeGroup)
      url.searchParams.set("db", currentDb)
      window.history.pushState({ group: activeGroup, db: currentDb }, "", url)

      // 隐藏搜索结果，显示网站容器
      searchResults.style.display = "none"
      websitesContainer.style.display = "grid"
    }

    // 处理浏览器前进/后退按钮
    window.addEventListener("popstate", (event) => {
      const urlParams = new URLSearchParams(window.location.search)
      const urlGroup = urlParams.get("group")
      const urlDb = urlParams.get("db")

      // 如果数据库发生变化，重新加载页面
      if (urlDb && urlDb !== currentDb) {
        window.location.reload()
        return
      }

      // 如果分组发生变化，切换分组
      if (urlGroup && urlGroup !== activeGroup) {
        // 检查分组是否存在
        const groupExists = document.querySelector(`[data-group="${urlGroup}"]`)
        if (groupExists) {
          activeGroup = urlGroup
          switchToGroup(urlGroup)
        }
      }
    })

    // 移动端菜单切换
    mobileMenuToggle.addEventListener("click", () => {
      sidebar.classList.toggle("mobile-open")
      sidebarOverlay.classList.toggle("show")
    })

    // 点击遮罩关闭侧边栏
    sidebarOverlay.addEventListener("click", () => {
      sidebar.classList.remove("mobile-open")
      sidebarOverlay.classList.remove("show")
    })

    // 在移动端点击分组后自动关闭侧边栏
    groupsList.addEventListener("click", (e) => {
      const groupItem = e.target.closest(".group-item")
      if (groupItem && window.innerWidth <= 768) {
        setTimeout(() => {
          sidebar.classList.remove("mobile-open")
          sidebarOverlay.classList.remove("show")
        }, 300) // 延迟关闭，让用户看到选中效果
      }
    })

    // 添加分组按钮点击事件
    addGroupBtn.addEventListener("click", () => {
      addGroupModal.style.display = "block"
    })
  
    // 搜索功能
    searchBtn.addEventListener("click", performSearch)
    searchInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        performSearch()
      }
    })
  
    function performSearch() {
      const query = searchInput.value.trim()
      if (!query) return
  
      loading.style.display = "flex"
      searchResults.innerHTML = ""
  
      fetch(`/api/search?q=${encodeURIComponent(query)}&db=${currentDb}`)
        .then((response) => response.json())
        .then((results) => {
          loading.style.display = "none"
  
          if (results.length === 0) {
            searchResults.innerHTML = '<div class="no-results">没有找到匹配的网站</div>'
          } else {
            results.forEach((website) => {
              searchResults.appendChild(createWebsiteCard(website, website.group))
            })
          }
  
          // 显示搜索结果，隐藏网站容器
          searchResults.style.display = "grid"
          websitesContainer.style.display = "none"
        })
        .catch((error) => {
          console.error("搜索出错:", error)
          loading.style.display = "none"
          searchResults.innerHTML = '<div class="no-results">搜索时出错</div>'
        })
    }
  
    // 添加网站按钮点击事件
    addWebsiteBtn.addEventListener("click", () => {
      // 重置表单
      addWebsiteForm.reset()
      imagePreview.src = "/static/img/default-icon.png"
      imageData.value = ""
  
      // 设置当前数据库
      document.getElementById("website-db").value = currentDb
  
      // 设置默认分组
      if (activeGroup) {
        websiteGroupSelect.value = activeGroup
      }
  
      // 打开模态框
      addWebsiteModal.style.display = "block"
    })
  
    // 管理按钮点击事件
    manageBtn.addEventListener("click", () => {
      isManageMode = !isManageMode
  
      if (isManageMode) {
        document.body.classList.add("manage-mode")
        manageBtn.innerHTML = '<i class="fas fa-times"></i> 退出管理'
        managementToolbar.style.display = "flex"
      } else {
        document.body.classList.remove("manage-mode")
        manageBtn.innerHTML = '<i class="fas fa-cog"></i> 管理'
        managementToolbar.style.display = "none"
        selectedCards.clear()
        updateSelectedCount()
  
        // 移除所有选中状态
        document.querySelectorAll(".website-card.selected").forEach((card) => {
          card.classList.remove("selected")
        })
      }
    })
  
    // 取消选择按钮点击事件
    cancelSelectionBtn.addEventListener("click", () => {
      selectedCards.clear()
      updateSelectedCount()
  
      // 移除所有选中状态
      document.querySelectorAll(".website-card.selected").forEach((card) => {
        card.classList.remove("selected")
      })
    })
  
    // 删除选中按钮点击事件
    deleteSelectedBtn.addEventListener("click", () => {
      if (selectedCards.size === 0) return
  
      if (confirm(`确定要删除选中的 ${selectedCards.size} 个网站吗？`)) {
        const cardsToDelete = Array.from(selectedCards)
        const group = cardsToDelete[0].dataset.group
        const indices = cardsToDelete.map((card) => Number.parseInt(card.dataset.index))
  
        deleteWebsites(group, indices)
      }
    })
  
    // 数据库选择器点击事件
    currentDbElement.addEventListener("click", () => {
      dbDropdown.classList.toggle("show")
    })
  
    // 点击其他地方关闭数据库下拉菜单
    document.addEventListener("click", (e) => {
      if (!e.target.closest(".db-selector")) {
        dbDropdown.classList.remove("show")
      }
    })
  
    // 数据库项点击事件
    dbDropdown.addEventListener("click", (e) => {
      const dbItem = e.target.closest(".db-item")
      if (!dbItem) return
  
      if (dbItem.classList.contains("new-db")) {
        // 打开新建数据库模态框
        addDbModal.style.display = "block"
        dbDropdown.classList.remove("show")
      } else if (dbItem.classList.contains("clean-images")) {
        // 打开清理图片模态框
        openCleanImagesModal()
        dbDropdown.classList.remove("show")
      } else if (dbItem.classList.contains("clean-navigation")) {
        // 打开清理导航模态框
        openCleanNavigationModal()
        dbDropdown.classList.remove("show")
      } else {
        // 切换数据库
        const dbName = dbItem.dataset.db
        if (dbName && dbName !== currentDb) {
          window.location.href = `/?db=${dbName}`
        }
      }
    })
  
    // 关闭模态框
    document
      .querySelectorAll(".close, #cancel-add, #cancel-add-group, #cancel-add-db, #cancel-clean-images")
      .forEach((element) => {
        element.addEventListener("click", () => {
          addWebsiteModal.style.display = "none"
          addGroupModal.style.display = "none"
          addDbModal.style.display = "none"
          cleanImagesModal.style.display = "none"
          imageViewerModal.style.display = "none"
        })
      })
  
    // 点击模态框外部关闭
    window.addEventListener("click", (e) => {
      if (e.target === addWebsiteModal) {
        addWebsiteModal.style.display = "none"
      }
      if (e.target === addGroupModal) {
        addGroupModal.style.display = "none"
      }
      if (e.target === addDbModal) {
        addDbModal.style.display = "none"
      }
      if (e.target === cleanImagesModal) {
        cleanImagesModal.style.display = "none"
      }
      if (e.target === imageViewerModal) {
        imageViewerModal.style.display = "none"
      }
    })
  
    // 新建分组按钮点击事件
    newGroupBtn.addEventListener("click", () => {
      addWebsiteModal.style.display = "none"
      addGroupModal.style.display = "block"
    })
  
    // 图片上传预览
    websiteImage.addEventListener("change", function () {
      const file = this.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          imagePreview.src = e.target.result
          imageData.value = e.target.result
        }
        reader.readAsDataURL(file)
      }
    })
  
    // 粘贴图片
    pasteImageBtn.addEventListener("click", () => {
      navigator.clipboard
        .read()
        .then((items) => {
          for (const item of items) {
            if (item.types.includes("image/png") || item.types.includes("image/jpeg")) {
              item.getType("image/png").then((blob) => {
                const reader = new FileReader()
                reader.onload = (e) => {
                  imagePreview.src = e.target.result
                  imageData.value = e.target.result
                }
                reader.readAsDataURL(blob)
              })
              return
            }
          }
          alert("剪贴板中没有图片")
        })
        .catch((err) => {
          console.error("无法读取剪贴板内容:", err)
          alert("无法读取剪贴板内容，请确保已授予权限")
        })
    })
  
    // 添加网站表单提交
    addWebsiteForm.addEventListener("submit", function (e) {
      e.preventDefault()
  
      const formData = new FormData(this)
  
      loading.style.display = "flex"
  
      fetch("/api/add", {
        method: "POST",
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          loading.style.display = "none"
          if (data.success) {
            addWebsiteModal.style.display = "none"
            // 如果当前不是该分组，切换到该分组
            const targetGroup = formData.get("group")
            if (activeGroup !== targetGroup) {
              switchToGroup(targetGroup)
            } else {
              // 重新加载当前分组
              loadGroupWebsites(activeGroup)
            }
          } else {
            alert("添加网站失败")
          }
        })
        .catch((error) => {
          console.error("添加网站出错:", error)
          loading.style.display = "none"
          alert("添加网站时出错")
        })
    })
  
    // 添加分组表单提交
addGroupForm.addEventListener("submit", (e) => {
  e.preventDefault()

  const groupName = document.getElementById("group-name").value.trim()
  if (!groupName) return

  loading.style.display = "flex"

  fetch("/api/add-group", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ name: groupName, db: currentDb }),
  })
        .then((response) => response.json())
        .then((data) => {
          loading.style.display = "none"
          if (data.success) {
            addGroupModal.style.display = "none"
  
            // 添加新分组到列表
            const newGroupItem = document.createElement("li")
            newGroupItem.className = "group-item"
            newGroupItem.dataset.group = groupName
            newGroupItem.innerHTML = `
                      <span class="group-name">${groupName}</span>
                      <span class="group-count" id="group-count-${groupName}">0</span>
                  `
  
            groupsList.appendChild(newGroupItem)
  
            // 添加到下拉列表
            const option = document.createElement("option")
            option.value = groupName
            option.textContent = groupName
            websiteGroupSelect.appendChild(option)
  
            // 如果是从添加网站模态框打开的，则返回到添加网站
            if (addWebsiteForm.style.display !== "none") {
              addWebsiteModal.style.display = "block"
              websiteGroupSelect.value = groupName
            }
          } else {
            alert(data.error || "添加分组失败")
          }
        })
        .catch((error) => {
          console.error("添加分组出错:", error)
          loading.style.display = "none"
          alert("添加分组时出错")
        })
    })
  
    // 添加数据库表单提交
    addDbForm.addEventListener("submit", (e) => {
      e.preventDefault()
  
      const dbName = document.getElementById("db-name").value.trim()
      if (!dbName) return
  
      loading.style.display = "flex"
  
      fetch("/api/create-database", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: dbName }),
      })
        .then((response) => response.json())
        .then((data) => {
          loading.style.display = "none"
          if (data.success) {
            addDbModal.style.display = "none"
            // 跳转到新数据库
            window.location.href = `/?db=${dbName}`
          } else {
            alert(data.error || "创建数据库失败")
          }
        })
        .catch((error) => {
          console.error("创建数据库出错:", error)
          loading.style.display = "none"
          alert("创建数据库时出错")
        })
    })
  
    // 打开清理图片模态框
    function openCleanImagesModal() {
      cleanImagesModal.style.display = "block"
      const unusedImagesContainer = document.getElementById("unused-images-container")
      unusedImagesContainer.innerHTML = `
              <div class="loading">
                  <div class="spinner"></div>
                  <p>正在扫描未使用的图片...</p>
              </div>
          `
  
      fetch(`/api/unused-images?db=${currentDb}`)
        .then((response) => response.json())
        .then((images) => {
          if (images.length === 0) {
            unusedImagesContainer.innerHTML = '<p class="no-results">没有找到未使用的图片</p>'
            document.getElementById("clean-images-btn").disabled = true
          } else {
            let html = ""
            images.forEach((image) => {
              html += `
                              <div class="unused-image-item">
                                  <input type="checkbox" class="image-checkbox" data-path="${image.path}" checked>
                                  <div class="image-preview">
                                      <img src="/static/${image.path}" alt="${image.filename}">
                                  </div>
                                  <div class="image-info">
                                      <div class="image-filename">${image.filename}</div>
                                      <div class="image-path">${image.path}</div>
                                  </div>
                              </div>
                          `
            })
            unusedImagesContainer.innerHTML = html
            document.getElementById("clean-images-btn").disabled = false
  
            // 添加复选框事件
            document.querySelectorAll(".image-checkbox").forEach((checkbox) => {
              checkbox.addEventListener("change", updateCleanButtonState)
            })
          }
        })
        .catch((error) => {
          console.error("获取未使用图片出错:", error)
          unusedImagesContainer.innerHTML = '<p class="no-results">获取未使用图片时出错</p>'
          document.getElementById("clean-images-btn").disabled = true
        })
    }
  
    // 更新清理按钮状态
    function updateCleanButtonState() {
      const checkedImages = document.querySelectorAll(".image-checkbox:checked")
      document.getElementById("clean-images-btn").disabled = checkedImages.length === 0
    }
  
    // 清理图片按钮点击事件
    document.getElementById("clean-images-btn").addEventListener("click", () => {
      const checkedImages = document.querySelectorAll(".image-checkbox:checked")
      if (checkedImages.length === 0) return
  
      if (confirm(`确定要删除选中的 ${checkedImages.length} 张图片吗？`)) {
        const imagesToDelete = Array.from(checkedImages).map((checkbox) => checkbox.dataset.path)
  
        loading.style.display = "flex"
  
        fetch("/api/clean-images", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ db: currentDb, images: imagesToDelete }),
        })
          .then((response) => response.json())
          .then((data) => {
            loading.style.display = "none"
            if (data.success) {
              alert(`成功删除 ${data.deleted} 张图片`)
              cleanImagesModal.style.display = "none"
            } else {
              alert("删除图片失败")
            }
          })
          .catch((error) => {
            console.error("删除图片出错:", error)
            loading.style.display = "none"
            alert("删除图片时出错")
          })
      }
    })
  
    // 加载分组网站
    function loadGroupWebsites(groupName) {
      loading.style.display = "flex"
      websitesContainer.innerHTML = ""
  
      fetch(`/api/group/${encodeURIComponent(groupName)}?db=${currentDb}`)
        .then((response) => response.json())
        .then((websites) => {
          loading.style.display = "none"
  
          if (websites.length === 0) {
            websitesContainer.innerHTML = '<div class="no-results">该分组没有网站</div>'
          } else {
            // 更新当前图片数组
            currentImages = websites.map((website) => ({
              src: `/static/${website.img}`,
              title: website.title,
            }))
  
            websites.forEach((website, index) => {
              websitesContainer.appendChild(createWebsiteCard(website, groupName, index))
            })
  
            // 更新分组计数
            updateGroupCount(groupName, websites.length)
  
            // 实现懒加载
            implementLazyLoading()
          }
        })
        .catch((error) => {
          console.error("加载网站出错:", error)
          loading.style.display = "none"
          websitesContainer.innerHTML = '<div class="no-results">加载网站时出错</div>'
        })
    }
  
    // 更新分组计数
    function updateGroupCount(groupName, count) {
      const countElement = document.getElementById(`group-count-${groupName}`)
      if (countElement) {
        countElement.textContent = count
      }
    }
  
    // 创建网站卡片
    function createWebsiteCard(website, groupName, index) {
      const card = document.createElement("div")
      card.className = "website-card"
      card.dataset.index = index
      card.dataset.group = groupName
  
      const cardHtml = `
              <div class="card-img">
                  <img src="/static/${website.img}" alt="${website.title}" loading="lazy" data-index="${index}">
              </div>
              <div class="card-content">
                  <a href="${website.url}" target="_blank"><h3 class="card-title" title="${website.title}">${website.title}</h3></a>
                  <p class="card-url" title="${website.url}">
                      <a href="${website.url}" target="_blank">${website.url}</a>
                  </p>
                  ${website.note ? `<p class="card-note">${website.note}</p>` : ""}
                  <div class="card-actions">
                      <span class="edit-btn" data-index="${index}" data-group="${groupName}">编辑</span>
                      <span class="delete-btn" data-index="${index}" data-group="${groupName}">删除</span>
                  </div>
              </div>
          `
  
      card.innerHTML = cardHtml
  
      // 添加删除事件
      card.querySelector(".delete-btn").addEventListener("click", function (e) {
        e.stopPropagation()
        const index = this.dataset.index
        const group = this.dataset.group
  
        if (confirm("确定要删除这个网站吗？")) {
          deleteWebsites(group, [Number.parseInt(index)])
        }
      })
  
      // 添加编辑事件
      card.querySelector(".edit-btn").addEventListener("click", function (e) {
        e.stopPropagation()
        const index = Number.parseInt(this.dataset.index)
        const group = this.dataset.group
  
        // 打开编辑模态框
        openEditWebsiteModal(group, index)
      })
  
      // 添加图片点击事件
      card.querySelector(".card-img img").addEventListener("click", function (e) {
        e.stopPropagation()
        const index = Number.parseInt(this.dataset.index)
        openImageViewer(index)
      })
  
      // 管理模式下的卡片点击事件
      card.addEventListener("click", function () {
        if (!isManageMode) return
  
        this.classList.toggle("selected")
  
        if (this.classList.contains("selected")) {
          selectedCards.add(this)
        } else {
          selectedCards.delete(this)
        }
  
        updateSelectedCount()
      })
  
      // 拖拽功能
      card.setAttribute("draggable", "true")
  
      card.addEventListener("dragstart", function (e) {
        if (!isManageMode) return
  
        e.dataTransfer.setData(
          "text/plain",
          JSON.stringify({
            indices: Array.from(selectedCards).map((card) => Number.parseInt(card.dataset.index)),
            group: groupName,
          }),
        )
  
        this.classList.add("dragging")
  
        // 如果当前卡片未被选中，则清除其他选中并选中当前卡片
        if (!this.classList.contains("selected")) {
          document.querySelectorAll(".website-card.selected").forEach((card) => {
            card.classList.remove("selected")
          })
          selectedCards.clear()
          this.classList.add("selected")
          selectedCards.add(this)
          updateSelectedCount()
        }
      })
  
      card.addEventListener("dragend", function () {
        this.classList.remove("dragging")
      })
  
      return card
    }
  
    // 更新选中计数
    function updateSelectedCount() {
      selectedCountSpan.textContent = selectedCards.size
    }
  
    // 删除网站
    function deleteWebsites(group, indices) {
      loading.style.display = "flex"
  
      fetch("/api/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ db: currentDb, group, indices }),
      })
        .then((response) => response.json())
        .then((data) => {
          loading.style.display = "none"
          if (data.success) {
            // 清除选中
            selectedCards.clear()
            updateSelectedCount()
  
            // 重新加载当前分组
            loadGroupWebsites(activeGroup)
          } else {
            alert(data.error || "删除网站失败")
          }
        })
        .catch((error) => {
          console.error("删除网站出错:", error)
          loading.style.display = "none"
          alert("删除网站时出错")
        })
    }
  
    // 实现懒加载
    function implementLazyLoading() {
      const cards = document.querySelectorAll(".website-card")
  
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const card = entry.target
              const img = card.querySelector('img[loading="lazy"]')
              if (img) {
                img.src = img.getAttribute("src")
                img.removeAttribute("loading")
              }
              observer.unobserve(card)
            }
          })
        },
        {
          root: null,
          rootMargin: "0px",
          threshold: 0.1,
        },
      )
  
      cards.forEach((card) => {
        observer.observe(card)
      })
    }
  
    // 打开图片查看器
    function openImageViewer(index) {
      if (currentImages.length === 0) return
  
      currentImageIndex = index
      const image = currentImages[index]
  
      viewerImage.src = image.src
      imageViewerTitle.textContent = image.title
  
      // 重置变换
      resetImageTransform()
  
      // 更新导航按钮状态
      updateNavigationButtons()
  
      // 显示模态框
      imageViewerModal.style.display = "block"
    }
  
    // 更新导航按钮状态
    function updateNavigationButtons() {
      prevImageBtn.style.visibility = currentImageIndex > 0 ? "visible" : "hidden"
      nextImageBtn.style.visibility = currentImageIndex < currentImages.length - 1 ? "visible" : "hidden"
    }
  
    // 重置图片变换
    function resetImageTransform() {
      imageScale = 1
      imageFlipH = false
      imageFlipV = false
      updateImageTransform()
    }
  
    // 更新��片变换
    function updateImageTransform() {
      let transform = `scale(${imageScale})`
      if (imageFlipH) transform += " scaleX(-1)"
      if (imageFlipV) transform += " scaleY(-1)"
      viewerImage.style.transform = transform
    }
  
    // 上一张图片
    prevImageBtn.addEventListener("click", () => {
      if (currentImageIndex > 0) {
        currentImageIndex--
        viewerImage.src = currentImages[currentImageIndex].src
        imageViewerTitle.textContent = currentImages[currentImageIndex].title
        resetImageTransform()
        updateNavigationButtons()
      }
    })
  
    // 下一张图片
    nextImageBtn.addEventListener("click", () => {
      if (currentImageIndex < currentImages.length - 1) {
        currentImageIndex++
        viewerImage.src = currentImages[currentImageIndex].src
        imageViewerTitle.textContent = currentImages[currentImageIndex].title
        resetImageTransform()
        updateNavigationButtons()
      }
    })
  
    // 水平翻转
    flipHBtn.addEventListener("click", () => {
      imageFlipH = !imageFlipH
      updateImageTransform()
    })
  
    // 垂直翻转
    flipVBtn.addEventListener("click", () => {
      imageFlipV = !imageFlipV
      updateImageTransform()
    })
  
    // 放大
    zoomInBtn.addEventListener("click", () => {
      imageScale = Math.min(imageScale * 1.2, 5)
      updateImageTransform()
    })
  
    // 缩小
    zoomOutBtn.addEventListener("click", () => {
      imageScale = Math.max(imageScale / 1.2, 0.2)
    })
  
    // 重置缩放
    zoomResetBtn.addEventListener("click", () => {
      resetImageTransform()
    })
  
    // 鼠标滚轮缩放
    document.getElementById("image-container").addEventListener("wheel", (e) => {
      e.preventDefault()
      if (e.deltaY < 0) {
        // 向上滚动，放大
        imageScale = Math.min(imageScale * 1.1, 5)
      } else {
        // 向下滚动，缩小
        imageScale = Math.max(imageScale / 1.1, 0.2)
      }
      updateImageTransform()
    })
  
    // 分组拖拽功能
    document.querySelectorAll(".group-item").forEach((group) => {
        group.addEventListener("dragover", function (e) {
            if (!isManageMode) return
            e.preventDefault()
            this.classList.add("drag-over")
        })

        group.addEventListener("dragleave", function () {
            this.classList.remove("drag-over")
        })

        group.addEventListener("drop", function (e) {
            if (!isManageMode) return
            e.preventDefault()
            this.classList.remove("drag-over")

            try {
                const data = JSON.parse(e.dataTransfer.getData("text/plain"))
                const fromGroup = data.group
                const toGroup = this.dataset.group
                const indices = data.indices

                if (fromGroup === toGroup || !indices.length) return

                // 移动网站到新分组
                moveWebsites(fromGroup, toGroup, indices)
            } catch (error) {
                console.error("拖放处理出错:", error)
            }
        })
    })

    // iframe宽度调整
    const nullboardIframe = document.getElementById('nullboard-iframe')
    //const iframeWidthDecreaseBtn = document.getElementById('iframe-width-decrease')
    //const iframeWidthIncreaseBtn = document.getElementById('iframe-width-increase')

    /*iframeWidthDecreaseBtn.addEventListener('click', () => {
        let currentWidth = parseInt(nullboardIframe.style.width)
        nullboardIframe.style.width = `${Math.max(200, currentWidth - 200)}px`
    })

    iframeWidthIncreaseBtn.addEventListener('click', () => {
        let currentWidth = parseInt(nullboardIframe.style.width)
        nullboardIframe.style.width = `${Math.min(800, currentWidth + 200)}px`
    })*/

    // 移动网站
    function moveWebsites(fromGroup, toGroup, indices) {
      loading.style.display = "flex"
  
      fetch("/api/move", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          db: currentDb,
          fromGroup,
          toGroup,
          indices,
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          loading.style.display = "none"
          if (data.success) {
            // 清除选中
            selectedCards.clear()
            updateSelectedCount()
  
            // 重新加载当前分组
            loadGroupWebsites(activeGroup)
          } else {
            alert(data.error || "移动网站失败")
          }
        })
        .catch((error) => {
          console.error("移动网站出错:", error)
          loading.style.display = "none"
          alert("移动网站时出错")
        })
    }
  
    // 打开清理导航模态框
    function openCleanNavigationModal() {
      // 创建模态框（如果不存在）
      if (!document.getElementById("clean-navigation-modal")) {
        const modalHtml = `
                  <div id="clean-navigation-modal" class="modal">
                      <div class="modal-content">
                          <span class="close">&times;</span>
                          <h2>清理导航</h2>
                          <div id="clean-navigation-container" class="clean-navigation-container">
                              <div class="loading">
                                  <div class="spinner"></div>
                                  <p>正在检查导航问题...</p>
                              </div>
                          </div>
                          <div class="form-actions">
                              <button type="button" class="btn cancel" id="cancel-clean-navigation">关闭</button>
                          </div>
                      </div>
                  </div>
              `
        document.body.insertAdjacentHTML("beforeend", modalHtml)
  
        // 添加关闭事件
        document.getElementById("cancel-clean-navigation").addEventListener("click", () => {
          document.getElementById("clean-navigation-modal").style.display = "none"
        })
  
        document.querySelector("#clean-navigation-modal .close").addEventListener("click", () => {
          document.getElementById("clean-navigation-modal").style.display = "none"
        })
  
        window.addEventListener("click", (e) => {
          if (e.target === document.getElementById("clean-navigation-modal")) {
            document.getElementById("clean-navigation-modal").style.display = "none"
          }
        })
      }
  
      const cleanNavigationModal = document.getElementById("clean-navigation-modal")
      const cleanNavigationContainer = document.getElementById("clean-navigation-container")
  
      cleanNavigationModal.style.display = "block"
      cleanNavigationContainer.innerHTML = `
              <div class="loading">
                  <div class="spinner"></div>
                  <p>正在检查导航问题...</p>
              </div>
          `
  
      fetch(`/api/check-duplicates?db=${currentDb}`)
        .then((response) => response.json())
        .then((data) => {
          let html = ""
  
          if (data.duplicates.length === 0 && data.empty_groups.length === 0) {
            html = '<p class="no-results">没有发现问题</p>'
          } else {
            // 显示重复URL
            if (data.duplicates.length > 0) {
              html += "<h3>重复的URL</h3>"
              data.duplicates.forEach((dup) => {
                html += `
                                  <div class="issue-item duplicate-item">
                                      <div class="issue-info">
                                          <div class="issue-title">URL: ${dup.url}</div>
                                          <div class="issue-detail">
                                              <p>1. 分组 "${dup.group1}" 中的 "${dup.title1}"</p>
                                              <p>2. 分组 "${dup.group2}" 中的 "${dup.title2}"</p>
                                          </div>
                                      </div>
                                      <div class="issue-actions">
                                          <button class="btn small danger delete-duplicate" 
                                                  data-group="${dup.group2}" 
                                                  data-index="${dup.index2}">
                                              删除第2项
                                          </button>
                                      </div>
                                  </div>
                              `
              })
            }
  
            // 显示空白分组
            if (data.empty_groups.length > 0) {
              html += "<h3>空白分组</h3>"
              data.empty_groups.forEach((group) => {
                html += `
                                  <div class="issue-item empty-group-item">
                                      <div class="issue-info">
                                          <div class="issue-title">分组: "${group.group}"</div>
                                      </div>
                                      <div class="issue-actions">
                                          <button class="btn small danger delete-empty-group" 
                                                  data-group="${group.group}">
                                              删除分组
                                          </button>
                                      </div>
                                  </div>
                              `
              })
            }
          }
  
          cleanNavigationContainer.innerHTML = html
  
          // 添加删除重复项事件
          document.querySelectorAll(".delete-duplicate").forEach((btn) => {
            btn.addEventListener("click", function () {
              const group = this.dataset.group
              const index = Number.parseInt(this.dataset.index)
  
              if (confirm("确定要删除这个重复的网站吗？")) {
                deleteWebsites(group, [index])
                // 删除后刷新列表
                setTimeout(() => {
                  openCleanNavigationModal()
                }, 500)
              }
            })
          })
  
          // 添加删除空白分组事件
          document.querySelectorAll(".delete-empty-group").forEach((btn) => {
            btn.addEventListener("click", function () {
              const group = this.dataset.group
  
              if (confirm(`确定要删除空白分组 "${group}" 吗？`)) {
                fetch("/api/delete-group", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({ db: currentDb, group }),
                })
                  .then((response) => response.json())
                  .then((data) => {
                    if (data.success) {
                      // 刷新分组列表
                      location.reload()
                    } else {
                      alert(data.error || "删除分组失败")
                    }
                  })
                  .catch((error) => {
                    console.error("删除分组出错:", error)
                    alert("删除分组时出错")
                  })
              }
            })
          })
        })
        .catch((error) => {
          console.error("检查导航问题出错:", error)
          cleanNavigationContainer.innerHTML = '<p class="no-results">检查导航问题时出错</p>'
        })
    }
  
    // 打开编辑网站模态框
    function openEditWebsiteModal(group, index) {
      // 创建模态框（如果不存在）
      if (!document.getElementById("edit-website-modal")) {
        const modalHtml = `
                  <div id="edit-website-modal" class="modal">
                      <div class="modal-content">
                          <span class="close">&times;</span>
                          <h2>编辑网站</h2>
                          <form id="edit-website-form">
                              <input type="hidden" id="edit-website-db" name="db" value="${currentDb}">
                              <input type="hidden" id="edit-website-group" name="group">
                              <input type="hidden" id="edit-website-index" name="index">
                              <div class="form-group">
                                  <label for="edit-website-title">网站名称</label>
                                  <input type="text" id="edit-website-title" name="title" required>
                              </div>
                              <div class="form-group">
                                  <label for="edit-website-url">网站地址</label>
                                  <input type="url" id="edit-website-url" name="url" required>
                              </div>
                              <div class="form-group">
                                  <label for="edit-website-note">备注</label>
                                  <textarea id="edit-website-note" name="note"></textarea>
                              </div>
                              <div class="form-group">
                                  <label>图标</label>
                                  <div class="image-upload">
                                      <div class="preview-container">
                                          <img id="edit-image-preview" src="/static/img/default-icon.png" alt="预览">
                                      </div>
                                      <div class="upload-actions">
                                          <input type="file" id="edit-website-image" name="image" accept="image/*">
                                          <label for="edit-website-image" class="btn small">选择图片</label>
                                          <button type="button" id="edit-paste-image-btn" class="btn small">粘贴图片</button>
                                          <input type="hidden" id="edit-image-data" name="image_data">
                                      </div>
                                  </div>
                              </div>
                              <div class="form-actions">
                                  <button type="submit" class="btn primary">保存</button>
                                  <button type="button" class="btn cancel" id="cancel-edit">取消</button>
                              </div>
                          </form>
                      </div>
                  </div>
              `
        document.body.insertAdjacentHTML("beforeend", modalHtml)
  
        // 添加关闭事件
        document.getElementById("cancel-edit").addEventListener("click", () => {
          document.getElementById("edit-website-modal").style.display = "none"
        })
  
        document.querySelector("#edit-website-modal .close").addEventListener("click", () => {
          document.getElementById("edit-website-modal").style.display = "none"
        })
  
        window.addEventListener("click", (e) => {
          if (e.target === document.getElementById("edit-website-modal")) {
            document.getElementById("edit-website-modal").style.display = "none"
          }
        })
  
        // 图片上传预览
        document.getElementById("edit-website-image").addEventListener("change", function () {
          const file = this.files[0]
          if (file) {
            const reader = new FileReader()
            reader.onload = (e) => {
              document.getElementById("edit-image-preview").src = e.target.result
              document.getElementById("edit-image-data").value = e.target.result
            }
            reader.readAsDataURL(file)
          }
        })
  
        // 粘贴图片
        document.getElementById("edit-paste-image-btn").addEventListener("click", () => {
          navigator.clipboard
            .read()
            .then((items) => {
              for (const item of items) {
                if (item.types.includes("image/png") || item.types.includes("image/jpeg")) {
                  item.getType("image/png").then((blob) => {
                    const reader = new FileReader()
                    reader.onload = (e) => {
                      document.getElementById("edit-image-preview").src = e.target.result
                      document.getElementById("edit-image-data").value = e.target.result
                    }
                    reader.readAsDataURL(blob)
                  })
                  return
                }
              }
              alert("剪贴板中没有图片")
            })
            .catch((err) => {
              console.error("无法读取剪贴板内容:", err)
              alert("无法读取剪贴板内容，请确保已授予权限")
            })
        })
  
        // 表单提交
        document.getElementById("edit-website-form").addEventListener("submit", function (e) {
          e.preventDefault()
  
          const formData = new FormData(this)
  
          loading.style.display = "flex"
  
          fetch("/api/edit", {
            method: "POST",
            body: formData,
          })
            .then((response) => response.json())
            .then((data) => {
              loading.style.display = "none"
              if (data.success) {
                document.getElementById("edit-website-modal").style.display = "none"
                // 重新加载当前分组
                loadGroupWebsites(activeGroup)
              } else {
                alert(data.error || "编辑网站失败")
              }
            })
            .catch((error) => {
              console.error("编辑网站出错:", error)
              loading.style.display = "none"
              alert("编辑网站时出错")
            })
        })
      }
  
      // 获取网站数据
      fetch(`/api/website?db=${currentDb}&group=${encodeURIComponent(group)}&index=${index}`)
        .then((response) => response.json())
        .then((website) => {
          if (website) {
            document.getElementById("edit-website-db").value = currentDb
            document.getElementById("edit-website-group").value = group
            document.getElementById("edit-website-index").value = index
            document.getElementById("edit-website-title").value = website.title
            document.getElementById("edit-website-url").value = website.url
            document.getElementById("edit-website-note").value = website.note || ""
            document.getElementById("edit-image-preview").src = `/static/${website.img}`
            document.getElementById("edit-image-data").value = ""
  
            document.getElementById("edit-website-modal").style.display = "block"
          } else {
            alert("获取网站信息失败")
          }
        })
        .catch((error) => {
          console.error("获取网站信息出错:", error)
          alert("获取网站信息时出错")
        })
    }
  
    // 在页面加载完成后添加CSS样式
    const style = document.createElement("style")
    style.textContent = `
          .db-item.clean-navigation {
              color: #4a6bff;
          }
          
          .clean-navigation-container {
              max-height: 400px;
              overflow-y: auto;
              margin-bottom: 20px;
          }
          
          .issue-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px;
              border-bottom: 1px solid var(--border-color);
          }
          
          .issue-item:last-child {
              border-bottom: none;
          }
          
          .issue-info {
              flex: 1;
          }
          
          .issue-title {
              font-weight: 500;
              margin-bottom: 5px;
          }
          
          .issue-detail {
              font-size: 13px;
              color: var(--light-text);
          }
          
          .issue-actions {
              margin-left: 15px;
          }
          
          .card-url a {
              color: var(--light-text);
              text-decoration: none;
          }
          
          .card-url a:hover {
              text-decoration: underline;
              color: var(--primary-color);
          }
          
          .edit-btn {
              color: var(--primary-color);
              cursor: pointer;
              display: none;
          }
          
          .edit-btn:hover {
              text-decoration: underline;
          }
          
          .manage-mode .edit-btn {
              display: inline;
          }
      `
    document.head.appendChild(style)
  
    /* 在页面加载完成后修改数据库下拉菜单
    const cleanNavigationItem = document.createElement("div")
    cleanNavigationItem.className = "db-item clean-navigation"
    cleanNavigationItem.innerHTML = '<i class="fas fa-broom"></i> 清理导航'
    document.querySelector(".db-item.clean-images").insertAdjacentElement("afterend", cleanNavigationItem)*/

    // 设置功能
    initSettings()
  })

  // 设置功能初始化
  function initSettings() {
    const settingsBtn = document.getElementById("settings-btn")
    const settingsModal = document.getElementById("settings-modal")
    const settingsForm = document.getElementById("settings-form")
    const cancelSettingsBtn = document.getElementById("cancel-settings")
    const resetSettingsBtn = document.getElementById("reset-settings")

    // 默认设置
    const defaultSettings = {
      cardSize: 'medium',
      cardGap: 20,
      groupHeight: 45,
      bgColor: '#f9fafc',
      cardBgColor: '#ffffff',
      textColor: '#333333',
      primaryColor: '#4a6bff'
    }

    // 加载保存的设置
    function loadSettings() {
      const saved = localStorage.getItem('navigationSettings')
      return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings
    }

    // 保存设置
    function saveSettings(settings) {
      localStorage.setItem('navigationSettings', JSON.stringify(settings))
    }

    // 应用设置
    function applySettings(settings) {
      const root = document.documentElement

      // 应用CSS变量
      root.style.setProperty('--dynamic-card-gap', settings.cardGap + 'px')
      root.style.setProperty('--dynamic-group-height', settings.groupHeight + 'px')
      root.style.setProperty('--dynamic-bg-color', settings.bgColor)
      root.style.setProperty('--dynamic-card-bg-color', settings.cardBgColor)
      root.style.setProperty('--dynamic-text-color', settings.textColor)
      root.style.setProperty('--dynamic-primary-color', settings.primaryColor)

      // 应用卡片大小类
      document.body.className = document.body.className.replace(/card-size-\w+/g, '')
      document.body.classList.add(`card-size-${settings.cardSize}`)

      // 更新主题色相关的CSS变量
      root.style.setProperty('--primary-color', settings.primaryColor)
      const primaryHover = adjustColor(settings.primaryColor, -20)
      root.style.setProperty('--primary-hover', primaryHover)
    }

    // 调整颜色亮度
    function adjustColor(color, amount) {
      const num = parseInt(color.replace("#", ""), 16)
      const amt = Math.round(2.55 * amount)
      const R = (num >> 16) + amt
      const G = (num >> 8 & 0x00FF) + amt
      const B = (num & 0x0000FF) + amt
      return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
    }

    // 更新表单值
    function updateFormValues(settings) {
      document.getElementById('card-size').value = settings.cardSize
      document.getElementById('card-gap').value = settings.cardGap
      document.getElementById('group-height').value = settings.groupHeight
      document.getElementById('bg-color').value = settings.bgColor
      document.getElementById('card-bg-color').value = settings.cardBgColor
      document.getElementById('text-color').value = settings.textColor
      document.getElementById('primary-color').value = settings.primaryColor

      // 更新范围值显示
      document.querySelector('#card-gap + .range-value').textContent = settings.cardGap + 'px'
      document.querySelector('#group-height + .range-value').textContent = settings.groupHeight + 'px'
    }

    // 初始化设置
    const currentSettings = loadSettings()
    applySettings(currentSettings)

    // 设置按钮点击事件
    settingsBtn.addEventListener('click', () => {
      updateFormValues(currentSettings)
      settingsModal.style.display = 'block'
    })

    // 取消按钮
    cancelSettingsBtn.addEventListener('click', () => {
      settingsModal.style.display = 'none'
    })

    // 重置按钮
    resetSettingsBtn.addEventListener('click', () => {
      updateFormValues(defaultSettings)
      applySettings(defaultSettings)
      saveSettings(defaultSettings)
      Object.assign(currentSettings, defaultSettings)
    })

    // 范围输入实时更新
    document.getElementById('card-gap').addEventListener('input', (e) => {
      document.querySelector('#card-gap + .range-value').textContent = e.target.value + 'px'
    })

    document.getElementById('group-height').addEventListener('input', (e) => {
      document.querySelector('#group-height + .range-value').textContent = e.target.value + 'px'
    })

    // 表单提交
    settingsForm.addEventListener('submit', (e) => {
      e.preventDefault()

      const formData = new FormData(settingsForm)
      const newSettings = {
        cardSize: formData.get('cardSize'),
        cardGap: parseInt(formData.get('cardGap')),
        groupHeight: parseInt(formData.get('groupHeight')),
        bgColor: formData.get('bgColor'),
        cardBgColor: formData.get('cardBgColor'),
        textColor: formData.get('textColor'),
        primaryColor: formData.get('primaryColor')
      }

      applySettings(newSettings)
      saveSettings(newSettings)
      Object.assign(currentSettings, newSettings)
      settingsModal.style.display = 'none'
    })

    // 点击模态框背景关闭
    settingsModal.addEventListener('click', (e) => {
      if (e.target === settingsModal) {
        settingsModal.style.display = 'none'
      }
    })
  }

