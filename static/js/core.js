// 全局变量和状态
let activeGroup
let currentDb
let isManageMode = false
const selectedCards = new Set()
const currentImages = []
let currentImageIndex = 0
let imageScale = 1
let imageFlipH = false
let imageFlipV = false

// DOM元素引用
let websitesContainer
let searchResults
let groupsList
let searchInput
let searchBtn
let addWebsiteBtn
let manageBtn
let addWebsiteModal
let addGroupModal
let addDbModal
let cleanImagesModal
let imageViewerModal
let addWebsiteForm
let addGroupForm
let addDbForm
let websiteGroupSelect
let newGroupBtn
let websiteImage
let imagePreview
let pasteImageBtn
let imageData
let loading
let managementToolbar
let selectedCountSpan
let cancelSelectionBtn
let deleteSelectedBtn
let currentDbElement
let dbDropdown
let addGroupBtn

// 图片查看器元素
let viewerImage
let prevImageBtn
let nextImageBtn
let flipHBtn
let flipVBtn
let zoomInBtn
let zoomOutBtn
let zoomResetBtn
let imageViewerTitle

// 初始化函数
function initializeApp() {
  // 获取DOM元素引用
  websitesContainer = document.getElementById("websites-container")
  searchResults = document.getElementById("search-results")
  groupsList = document.getElementById("groups-list")
  searchInput = document.getElementById("search-input")
  searchBtn = document.getElementById("search-btn")
  addWebsiteBtn = document.getElementById("add-website-btn")
  manageBtn = document.getElementById("manage-btn")
  addWebsiteModal = document.getElementById("add-website-modal")
  addGroupModal = document.getElementById("add-group-modal")
  addDbModal = document.getElementById("add-db-modal")
  cleanImagesModal = document.getElementById("clean-images-modal")
  imageViewerModal = document.getElementById("image-viewer-modal")
  addWebsiteForm = document.getElementById("add-website-form")
  addGroupForm = document.getElementById("add-group-form")
  addDbForm = document.getElementById("add-db-form")
  websiteGroupSelect = document.getElementById("website-group")
  newGroupBtn = document.getElementById("new-group-btn")
  websiteImage = document.getElementById("website-image")
  imagePreview = document.getElementById("image-preview")
  pasteImageBtn = document.getElementById("paste-image-btn")
  imageData = document.getElementById("image-data")
  loading = document.getElementById("loading")
  managementToolbar = document.getElementById("management-toolbar")
  selectedCountSpan = document.getElementById("selected-count")
  cancelSelectionBtn = document.getElementById("cancel-selection-btn")
  deleteSelectedBtn = document.getElementById("delete-selected-btn")
  currentDbElement = document.getElementById("current-db")
  dbDropdown = document.getElementById("db-dropdown")
  addGroupBtn = document.getElementById("add-group-btn")

  // 图片查看器元素
  viewerImage = document.getElementById("viewer-image")
  prevImageBtn = document.getElementById("prev-image-btn")
  nextImageBtn = document.getElementById("next-image-btn")
  flipHBtn = document.getElementById("flip-h-btn")
  flipVBtn = document.getElementById("flip-v-btn")
  zoomInBtn = document.getElementById("zoom-in-btn")
  zoomOutBtn = document.getElementById("zoom-out-btn")
  zoomResetBtn = document.getElementById("zoom-reset-btn")
  imageViewerTitle = document.getElementById("image-viewer-title")

  // 初始化状态变量
  activeGroup = document.querySelector(".group-item.active")?.dataset.group
  currentDb = document.getElementById("website-db").value

  // 添加CSS样式
  addCustomStyles()

  // 初始化事件监听器
  initEventListeners()

  // 加载当前分组的网站
  if (activeGroup) {
    loadGroupWebsites(activeGroup)
  }
}

// 初始化事件监听器
function initEventListeners() {
  // 分组点击事件
  groupsList.addEventListener("click", handleGroupClick)

  // 添加分组按钮点击事件
  addGroupBtn.addEventListener("click", () => {
    addGroupModal.style.display = "block"
  })

  // 搜索功能
  searchBtn.addEventListener("click", performSearch)
  searchInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      performSearch()
    }
  })

  // 添加网站按钮点击事件
  addWebsiteBtn.addEventListener("click", handleAddWebsiteClick)

  // 管理按钮点击事件
  manageBtn.addEventListener("click", toggleManageMode)

  // 取消选择按钮点击事件
  cancelSelectionBtn.addEventListener("click", clearSelection)

  // 删除选中按钮点击事件
  deleteSelectedBtn.addEventListener("click", deleteSelectedItems)

  // 数据库选择器点击事件
  currentDbElement.addEventListener("click", () => {
    dbDropdown.classList.toggle("show")
  })

  // 点击其他地方关闭数据库下拉菜单
  document.addEventListener("click", (e) => {
    if (!e.target.closest(".db-selector")) {
      dbDropdown.classList.remove("show")
    }
  })

  // 数据库项点击事件
  dbDropdown.addEventListener("click", handleDbItemClick)

  // 关闭模态框
  document
    .querySelectorAll(".close, #cancel-add, #cancel-add-group, #cancel-add-db, #cancel-clean-images")
    .forEach((element) => {
      element.addEventListener("click", () => {
        addWebsiteModal.style.display = "none"
        addGroupModal.style.display = "none"
        addDbModal.style.display = "none"
        cleanImagesModal.style.display = "none"
        imageViewerModal.style.display = "none"
      })
    })

  // 点击模态框外部关闭
  window.addEventListener("click", handleModalOutsideClick)

  // 新建分组按钮点击事件
  newGroupBtn.addEventListener("click", () => {
    addWebsiteModal.style.display = "none"
    addGroupModal.style.display = "block"
  })

  // 图片上传预览
  websiteImage.addEventListener("change", handleImageUpload)

  // 粘贴图片
  pasteImageBtn.addEventListener("click", handlePasteImage)

  // 添加网站表单提交
  addWebsiteForm.addEventListener("submit", handleAddWebsiteSubmit)

  // 添加分组表单提交
  addGroupForm.addEventListener("submit", handleAddGroupSubmit)

  // 添加数据库表单提交
  addDbForm.addEventListener("submit", handleAddDbSubmit)

  // 清理图片按钮点击事件
  document.getElementById("clean-images-btn")?.addEventListener("click", handleCleanImagesClick)

  // 图片查看器控制按钮事件
  initImageViewerEvents()
}

// 初始化图片查看器事件
function initImageViewerEvents() {
  // 上一张图片
  prevImageBtn.addEventListener("click", () => {
    if (currentImageIndex > 0) {
      currentImageIndex--
      viewerImage.src = currentImages[currentImageIndex].src
      imageViewerTitle.textContent = currentImages[currentImageIndex].title
      resetImageTransform()
      updateNavigationButtons()
    }
  })

  // 下一张图片
  nextImageBtn.addEventListener("click", () => {
    if (currentImageIndex < currentImages.length - 1) {
      currentImageIndex++
      viewerImage.src = currentImages[currentImageIndex].src
      imageViewerTitle.textContent = currentImages[currentImageIndex].title
      resetImageTransform()
      updateNavigationButtons()
    }
  })

  // 水平翻转
  flipHBtn.addEventListener("click", () => {
    imageFlipH = !imageFlipH
    updateImageTransform()
  })

  // 垂直翻转
  flipVBtn.addEventListener("click", () => {
    imageFlipV = !imageFlipV
    updateImageTransform()
  })

  // 放大
  zoomInBtn.addEventListener("click", () => {
    imageScale = Math.min(imageScale * 1.2, 5)
    updateImageTransform()
  })

  // 缩小
  zoomOutBtn.addEventListener("click", () => {
    imageScale = Math.max(imageScale / 1.2, 0.2)
    updateImageTransform()
  })

  // 重置缩放
  zoomResetBtn.addEventListener("click", () => {
    resetImageTransform()
  })

  // 鼠标滚轮缩放
  document.getElementById("image-container")?.addEventListener("wheel", (e) => {
    e.preventDefault()
    if (e.deltaY < 0) {
      // 向上滚动，放大
      imageScale = Math.min(imageScale * 1.1, 5)
    } else {
      // 向下滚动，缩小
      imageScale = Math.max(imageScale / 1.1, 0.2)
    }
    updateImageTransform()
  })
}

// 处理分组点击
function handleGroupClick(e) {
  const groupItem = e.target.closest(".group-item")
  if (!groupItem) return

  // 切换分组
  document.querySelectorAll(".group-item").forEach((item) => {
    item.classList.remove("active")
  })
  groupItem.classList.add("active")

  activeGroup = groupItem.dataset.group
  loadGroupWebsites(activeGroup)

  // 更新URL参数
  const url = new URL(window.location)
  url.searchParams.set("group", activeGroup)
  window.history.pushState({}, "", url)

  // 隐藏搜索结果，显示网站容器
  searchResults.style.display = "none"
  websitesContainer.style.display = "grid"
}

// 处理添加网站按钮点击
function handleAddWebsiteClick() {
  // 重置表单
  addWebsiteForm.reset()
  imagePreview.src = "/static/img/default-icon.png"
  imageData.value = ""

  // 设置当前数据库
  document.getElementById("website-db").value = currentDb

  // 设置默认分组
  if (activeGroup) {
    websiteGroupSelect.value = activeGroup
  }

  // 打开模态框
  addWebsiteModal.style.display = "block"
}

// 切换管理模式
function toggleManageMode() {
  isManageMode = !isManageMode

  if (isManageMode) {
    document.body.classList.add("manage-mode")
    manageBtn.innerHTML = '<i class="fas fa-times"></i> 退出管理'
    managementToolbar.style.display = "flex"
  } else {
    document.body.classList.remove("manage-mode")
    manageBtn.innerHTML = '<i class="fas fa-cog"></i> 管理'
    managementToolbar.style.display = "none"
    clearSelection()
  }
}

// 清除选中
function clearSelection() {
  selectedCards.clear()
  updateSelectedCount()

  // 移除所有选中状态
  document.querySelectorAll(".website-card.selected").forEach((card) => {
    card.classList.remove("selected")
  })
}

// 删除选中项
function deleteSelectedItems() {
  if (selectedCards.size === 0) return

  if (confirm(`确定要删除选中的 ${selectedCards.size} 个网站吗？`)) {
    const cardsToDelete = Array.from(selectedCards)
    const group = cardsToDelete[0].dataset.group
    const indices = cardsToDelete.map((card) => Number.parseInt(card.dataset.index))

    deleteWebsites(group, indices)
  }
}

// 处理数据库项点击
function handleDbItemClick(e) {
  const dbItem = e.target.closest(".db-item")
  if (!dbItem) return

  if (dbItem.classList.contains("new-db")) {
    // 打开新建数据库模态框
    addDbModal.style.display = "block"
    dbDropdown.classList.remove("show")
  } else if (dbItem.classList.contains("clean-navigation")) {
    // 打开清理导航模态框
    openCleanNavigationModal()
    dbDropdown.classList.remove("show")
  } else if (dbItem.classList.contains("clean-images")) {
    // 打开清理图片模态框
    openCleanImagesModal()
    dbDropdown.classList.remove("show")
  } else {
    // 切换数据库
    const dbName = dbItem.dataset.db
    if (dbName && dbName !== currentDb) {
      window.location.href = `/?db=${dbName}`
    }
  }
}

// 处理模态框外部点击
function handleModalOutsideClick(e) {
  if (e.target === addWebsiteModal) {
    addWebsiteModal.style.display = "none"
  }
  if (e.target === addGroupModal) {
    addGroupModal.style.display = "none"
  }
  if (e.target === addDbModal) {
    addDbModal.style.display = "none"
  }
  if (e.target === cleanImagesModal) {
    cleanImagesModal.style.display = "none"
  }
  if (e.target === imageViewerModal) {
    imageViewerModal.style.display = "none"
  }
}

// 处理图片上传
function handleImageUpload() {
  const file = this.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.src = e.target.result
      imageData.value = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

// 处理粘贴图片
function handlePasteImage() {
  navigator.clipboard
    .read()
    .then((items) => {
      for (const item of items) {
        if (item.types.includes("image/png") || item.types.includes("image/jpeg")) {
          item.getType("image/png").then((blob) => {
            const reader = new FileReader()
            reader.onload = (e) => {
              imagePreview.src = e.target.result
              imageData.value = e.target.result
            }
            reader.readAsDataURL(blob)
          })
          return
        }
      }
      alert("剪贴板中没有图片")
    })
    .catch((err) => {
      console.error("无法读取剪贴板内容:", err)
      alert("无法读取剪贴板内容，请确保已授予权限")
    })
}

// 添加自定义样式
function addCustomStyles() {
  const style = document.createElement("style")
  style.textContent = `
        .db-item.clean-navigation {
            color: #4a6bff;
        }
        
        .clean-navigation-container {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .issue-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .issue-item:last-child {
            border-bottom: none;
        }
        
        .issue-info {
            flex: 1;
        }
        
        .issue-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .issue-detail {
            font-size: 13px;
            color: var(--light-text);
        }
        
        .issue-actions {
            margin-left: 15px;
        }
        
        .card-url a {
            color: var(--light-text);
            text-decoration: none;
        }
        
        .card-url a:hover {
            text-decoration: underline;
            color: var(--primary-color);
        }
        
        .edit-btn {
            color: var(--primary-color);
            cursor: pointer;
            display: none;
        }
        
        .edit-btn:hover {
            text-decoration: underline;
        }
        
        .manage-mode .edit-btn {
            display: inline;
        }
        
        .card-note {
            cursor: pointer;
            position: relative;
        }
        
        .card-note:hover {
            color: var(--primary-color);
        }
        
        .card-note:hover::after {
            content: "点击查看/编辑";
            position: absolute;
            top: -20px;
            left: 0;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .note-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 15px;
        }
        
        .note-tab {
            padding: 8px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        
        .note-tab.active {
            border-bottom-color: #4a6bff;
            color: #4a6bff;
        }
        
        .note-tab-content {
            display: none;
        }
        
        .note-tab-content.active {
            display: block;
        }
        
        .note-viewer-content {
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .note-editor {
            width: 100%;
            min-height: 200px;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
        }
        
        .markdown-toolbar {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .markdown-btn {
            padding: 5px 10px;
            background-color: #f0f0f0;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .markdown-btn:hover {
            background-color: #e0e0e0;
        }
    `
  document.head.appendChild(style)
}

// 在DOM加载完成后初始化应用
document.addEventListener("DOMContentLoaded", initializeApp)

// 声明缺失的函数
function handleCleanImagesClick() {
  // 函数体需要根据实际情况添加
  console.log("handleCleanImagesClick function called")
}

function resetImageTransform() {
  imageScale = 1
  imageFlipH = false
  imageFlipV = false
  updateImageTransform()
}

function updateNavigationButtons() {
  prevImageBtn.disabled = currentImageIndex === 0
  nextImageBtn.disabled = currentImageIndex === currentImages.length - 1
}

function updateImageTransform() {
  viewerImage.style.transform = `scale(${imageScale}) translate(${imageFlipH ? "-100%" : "0"}) scaleX(${imageFlipH ? -1 : 1}) scaleY(${imageFlipV ? -1 : 1})`
}

function updateSelectedCount() {
  selectedCountSpan.textContent = selectedCards.size
}

function openCleanNavigationModal() {
  cleanImagesModal.style.display = "block"
}

function openCleanImagesModal() {
  cleanImagesModal.style.display = "block"
}

function performSearch() {
  console.log("performSearch function called")
}

function handleAddWebsiteSubmit() {
  console.log("handleAddWebsiteSubmit function called")
}

function handleAddGroupSubmit() {
  console.log("handleAddGroupSubmit function called")
}

function handleAddDbSubmit() {
  console.log("handleAddDbSubmit function called")
}

function deleteWebsites() {
  console.log("deleteWebsites function called")
}

function loadGroupWebsites() {
  console.log("loadGroupWebsites function called")
}

