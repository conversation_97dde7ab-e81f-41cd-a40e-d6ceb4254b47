<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏按钮演示页面</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #4a6bff;
        }
        
        .demo-section h2 {
            color: #4a6bff;
            margin-top: 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            color: #856404;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✅ ";
            margin-right: 8px;
        }
        
        .notice {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .notice strong {
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 导航收藏小工具演示</h1>
        
        <div class="notice">
            <strong>注意：</strong>请确保导航系统服务已启动（默认端口4157），否则收藏功能无法正常工作。
        </div>
        
        <div class="demo-section">
            <h2>🎯 功能特点</h2>
            <ul class="feature-list">
                <li>悬浮在页面右侧的隐藏收藏按钮</li>
                <li>鼠标悬停时按钮完全显示</li>
                <li>点击后弹出收藏对话框</li>
                <li>自动获取当前页面标题和URL</li>
                <li>支持选择数据库和分组</li>
                <li>支持创建新分组</li>
                <li>自动获取网页图标</li>
                <li>与导航系统API完全对接</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🚀 使用方法</h2>
            <p>在任何网页中引入以下JavaScript文件即可：</p>
            <div class="code-block">
&lt;script src="http://localhost:4157/static/js/bookmark-widget.js"&gt;&lt;/script&gt;
            </div>
            <p>或者直接复制JavaScript代码到网页中。</p>
        </div>
        
        <div class="demo-section">
            <h2>🎮 体验演示</h2>
            <p>在当前页面右侧可以看到一个<span class="highlight">⭐</span>收藏按钮，将鼠标移动到页面右边缘即可看到。</p>
            <p>点击按钮即可体验收藏功能！</p>

            <h3>新增功能测试：</h3>
            <ul class="feature-list">
                <li>点击"选择图片"按钮上传自定义图标</li>
                <li>复制图片到剪贴板，然后点击"粘贴图片"按钮</li>
                <li>查看收藏对话框中的图片预览功能</li>
                <li>测试数据是否正确保存到导航系统</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>⚙️ 配置选项</h2>
            <p>可以通过修改JavaScript文件中的CONFIG对象来自定义：</p>
            <div class="code-block">
const CONFIG = {
    API_BASE: 'http://localhost:4157',  // API地址
    BUTTON_SIZE: '50px',                // 按钮大小
    BUTTON_COLOR: '#4a6bff',           // 按钮颜色
    BUTTON_HOVER_COLOR: '#3a5bef',     // 悬停颜色
    Z_INDEX: 999999                    // 层级
};
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔧 技术实现</h2>
            <ul class="feature-list">
                <li>纯JavaScript实现，无依赖</li>
                <li>自动防止重复加载</li>
                <li>响应式设计，适配移动端</li>
                <li>优雅的动画效果</li>
                <li>完善的错误处理</li>
                <li>支持ESC键关闭对话框</li>
                <li>支持点击背景关闭对话框</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>📱 浏览器扩展</h2>
            <p>此收藏小工具可以作为浏览器扩展的核心功能，也可以直接在网页中使用。</p>
            <p>对于需要在所有网站上使用的场景，建议开发浏览器扩展。</p>
        </div>
    </div>
    
    <!-- 引入收藏小工具 -->
    <script src="js/bookmark-widget.js"></script>
</body>
</html>
