<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji番茄时钟 - 专注提醒器</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: white;
            overflow-x: hidden;
            transition: all 0.5s ease;
        }
        
        .container {
            max-width: 1000px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            animation: fadeIn 1s ease-out;
            transition: all 0.5s ease;
        }
        
        h1 {
            font-size: 3.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            line-height: 1.6;
            margin-top: 15px;
        }
        
        /* 设置区域 */
        .settings {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.18);
            margin-bottom: 30px;
            animation: slideUp 0.8s ease-out;
            transition: all 0.5s ease;
        }
        
        .setting-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        input, textarea {
            width: 100%;
            padding: 14px;
            border-radius: 15px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 1.1rem;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        
        input:focus, textarea:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
        }
        
        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 14px 35px;
            font-size: 1.1rem;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(to right, #FF416C, #FF4B2B);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 75, 43, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(255, 75, 43, 0.6);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.15);
            color: white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        /* 计时器区域 */
        .timer-area {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            padding: 30px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;
            animation: fadeIn 1.2s ease-out;
            transition: all 0.5s ease;
        }
        
        .timer-display {
            font-size: 4.5rem;
            font-weight: 700;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
            transition: all 0.5s ease;
        }
        
        .progress-container {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, #00b09b, #96c93d);
            border-radius: 10px;
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .timer-status {
            font-size: 1.4rem;
            margin: 15px 0;
            min-height: 40px;
            text-align: center;
            font-weight: 500;
        }
        
        .timer-controls {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }
        
        /* 底部Emoji进度区 */
        .emoji-track {
            width: 100%;
            min-height: 120px;
            background: rgba(0, 0, 0, 0.15);
            border-radius: 15px;
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.5s ease;
        }
        
        .emoji-container {
            position: relative;
            display: inline-block;
            animation: popIn 0.5s ease forwards;
        }
        
        .emoji-bubble {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 12px 18px;
            border-radius: 20px;
            margin-bottom: 10px;
            font-size: 1rem;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            min-width: 180px;
            text-align: center;
            animation: fadeInDown 0.5s ease-out;
            display: none;
        }
        
        .emoji-container.active .emoji-bubble {
            display: block;
        }
        
        .emoji-bubble::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 10px solid transparent;
            border-top-color: rgba(255, 255, 255, 0.9);
        }
        
        .track-emoji {
            font-size: 3rem;
            text-align: center;
            cursor: pointer;
        }
        
        /* 大Emoji动画 */
        .big-emoji {
            position: fixed;
            top: -200px;
            font-size: 0;
            opacity: 0;
            z-index: 100;
            transition: font-size 0.01s;
            pointer-events: none;
            cursor: pointer;
            text-shadow: 0 0 30px rgba(255,255,255,0.8);
        }
        
        .animate-emoji {
            animation: dropDown 2s forwards;
            pointer-events: auto;
        }
        
        /* 计时器激活状态 */
        body.timer-active {
            padding: 10px;
        }
        
        body.timer-active header {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        body.timer-active .settings {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        body.timer-active .timer-area {
            flex: 1;
            max-width: 800px;
            margin: 20px 0;
            padding: 50px 30px;
        }
        
        body.timer-active .timer-display {
            font-size: 6rem;
        }
        
        body.timer-active .emoji-track {
            min-height: 150px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }
            
            .timer-display {
                font-size: 3.5rem;
            }
            
            body.timer-active .timer-display {
                font-size: 4rem;
            }
            
            .settings {
                padding: 20px;
            }
            
            .controls {
                flex-direction: column;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
            
            .emoji-track {
                min-height: 150px;
                gap: 25px;
            }
        }
        
        @media (max-width: 480px) {
            h1 {
                font-size: 2rem;
            }
            
            .timer-display {
                font-size: 2.8rem;
            }
            
            body.timer-active .timer-display {
                font-size: 3.2rem;
            }
            
            .emoji-bubble {
                font-size: 0.9rem;
                min-width: 150px;
                padding: 10px 15px;
            }
            
            .track-emoji {
                font-size: 2.5rem;
            }
        }
        
        /* 动画 */
        @keyframes dropDown {
            0% {
                top: -200px;
                font-size: 0;
                opacity: 0;
            }
            30% {
                opacity: 1;
                font-size: 8rem;
            }
            100% {
                top: 50%;
                transform: translateY(-50%);
                font-size: 15rem;
                opacity: 1;
            }
        }
        
        @keyframes popIn {
            0% { transform: scale(0); opacity: 0; }
            70% { transform: scale(1.2); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translate(-50%, -20px);
            }
            to {
                opacity: 1;
                transform: translate(-50%, 0);
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .info-text {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 12px;
            margin-top: 20px;
            font-size: 0.95rem;
            line-height: 1.6;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            opacity: 1;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="close-btn" id="close-btn" style="display: none;">
            <i class="fas fa-times"></i>
        </button>
        
        <header>
            <h1><span>🍅</span> Emoji番茄时钟</h1>
            <p class="subtitle">设置专注时长和提醒内容，系统将随机选择Emoji作为时间提醒。每分钟底部会出现一个Emoji和对话泡提醒您时间流逝！</p>
        </header>
        
        <div class="settings">
            <div class="setting-group">
                <label for="minutes"><i class="fas fa-clock"></i> 专注时长（分钟）</label>
                <input type="number" id="minutes" min="1" max="120" value="25" placeholder="输入分钟数（1-120）">
            </div>
            
            <div class="setting-group">
                <label for="reminder"><i class="fas fa-comment-alt"></i> 提醒内容</label>
                <textarea id="reminder" placeholder="输入提醒内容，例如：是否在专注工作？">是否在专注做预定的事？</textarea>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" id="start-btn">
                    <i class="fas fa-play"></i> 开始专注
                </button>
                <button class="btn btn-secondary" id="reset-btn">
                    <i class="fas fa-redo"></i> 重置
                </button>
            </div>
            
            <div class="info-text">
                <p><i class="fas fa-info-circle"></i> 提示：每过一分钟，页面底部会出现一个随机Emoji和您设置的提醒内容。结束时，会有一个生物类Emoji（如人物、动物等）降落，点击它后才会消失！</p>
            </div>
        </div>
        
        <div class="timer-area">
            <div class="timer-display" id="timer">25:00</div>
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            <div class="timer-status" id="timer-status">设置专注时长并开始计时</div>
            
            <div class="timer-controls">
                <button class="btn btn-secondary" id="pause-btn" style="display: none;">
                    <i class="fas fa-pause"></i> 暂停
                </button>
            </div>
        </div>
        
        <div class="emoji-track" id="emoji-track">
            <div id="track-placeholder">Emoji将在这里显示，每分钟出现一个</div>
        </div>
        
        <div class="big-emoji" id="big-emoji"></div>
    </div>

    <audio id="sound" src="https://assets.mixkit.co/sfx/preview/mixkit-alarm-digital-clock-beep-989.mp3"></audio>
    <audio id="end-sound" src="https://assets.mixkit.co/sfx/preview/mixkit-winning-chimes-2015.mp3"></audio>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 定义生物类emoji（用于最后一个）
            const livingEmojis = [
                "🧑", "👨", "👩", "👧", "👦", "👶", "🧑‍🚀", "🧑‍🚒", "👮", "👷",
                "💂", "🕵️", "👩‍⚕️", "👨‍🍳", "👩‍🎓", "👨‍🏫", "👩‍🏭", "👨‍💻", "👩‍💼", "👨‍🔧",
                "👩‍🔬", "👨‍🎨", "👩‍🚒", "👨‍✈️", "👩‍🚀", "🧙", "🧚", "🧛", "🧜", "🧝",
                "🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯",
                "🦁", "🐮", "🐷", "🐸", "🐵", "🐔", "🐧", "🐦", "🦆", "🦅",
                "🦉", "🦇", "🐺", "🐗", "🐴", "🦄", "🐝", "🐛", "🦋", "🐌",
                "🐞", "🐜", "🦗", "🕷", "🦂", "🦀", "🐍", "🦎", "🐊", "🐅",
                "🦓", "🦍", "🦧", "🦣", "🐘", "🦛", "🦏", "🐪", "🐫", "🦒",
                "🦘", "🐃", "🐂", "🐄", "🐎", "🐖", "🐏", "🐑", "🦙", "🐐"
            ];
            
            // 定义普通emoji
            const normalEmojis = [
                "🍎", "🍐", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🫐", "🍈",
                "🍒", "🍑", "🥭", "🍍", "🥥", "🥝", "🍆", "🥑", "🥦", "🥬",
                "🥒", "🌶", "🫑", "🌽", "🥕", "🫒", "🧄", "🧅", "🥔", "🍠",
                "🥐", "🥯", "🍞", "🥖", "🥨", "🧀", "🥚", "🍳", "🧈", "🥞",
                "🧇", "🥓", "🥩", "🍗", "🍖", "🌭", "🍔", "🍟", "🍕", "🫓",
                "🥪", "🥙", "🧆", "🌮", "🌯", "🫔", "🥗", "🥘", "🫕", "🥫",
                "🍝", "🍜", "🍲", "🍛", "🍣", "🍱", "🥟", "🦪", "🍤", "🍙",
                "🍚", "🍘", "🍥", "🥠", "🥮", "🍢", "🍡", "🍧", "🍨", "🍦",
                "🥧", "🧁", "🍰", "🎂", "🍮", "🍭", "🍬", "🍫", "🍿", "🍩",
                "🍪", "🌰", "🥜", "🫘", "☕", "🍵", "🧃", "🥤", "🚗", "✈️"
            ];
            
            const minutesInput = document.getElementById('minutes');
            const reminderInput = document.getElementById('reminder');
            const emojiTrack = document.getElementById('emoji-track');
            const trackPlaceholder = document.getElementById('track-placeholder');
            const timerElement = document.getElementById('timer');
            const timerStatus = document.getElementById('timer-status');
            const progressBar = document.getElementById('progress-bar');
            const startBtn = document.getElementById('start-btn');
            const resetBtn = document.getElementById('reset-btn');
            const pauseBtn = document.getElementById('pause-btn');
            const bigEmoji = document.getElementById('big-emoji');
            const sound = document.getElementById('sound');
            const endSound = document.getElementById('end-sound');
            const closeBtn = document.getElementById('close-btn');
            
            let selectedEmojis = [];
            let timer;
            let totalMinutes = 25;
            let totalSeconds = totalMinutes * 60;
            let remainingSeconds = totalSeconds;
            let isRunning = false;
            let isPaused = false;
            let minutesPassed = 0;
            let userReminder = "是否在专注做预定的事？";
            let lastEmoji = null;
            
            // 初始化显示
            updateTimerDisplay();
            
            // 开始计时
            startBtn.addEventListener('click', () => {
                if (isRunning && !isPaused) return;
                
                // 获取用户输入
                totalMinutes = parseInt(minutesInput.value) || 25;
                if (totalMinutes < 1) totalMinutes = 1;
                if (totalMinutes > 120) totalMinutes = 120;
                
                userReminder = reminderInput.value || "是否在专注做预定的事？";
                
                // 激活计时器状态
                document.body.classList.add('timer-active');
                closeBtn.style.display = 'block';
                
                // 重置状态
                if (!isRunning) {
                    // 新计时开始
                    totalSeconds = totalMinutes * 60;
                    remainingSeconds = totalSeconds;
                    minutesPassed = 0;
                    
                    // 生成随机emoji
                    generateRandomEmojis(totalMinutes);
                    
                    // 清空emoji轨道
                    trackPlaceholder.style.display = 'none';
                    emojiTrack.innerHTML = '';
                }
                
                isRunning = true;
                isPaused = false;
                startBtn.style.display = 'none';
                pauseBtn.style.display = 'flex';
                
                updateTimerDisplay();
                progressBar.style.width = '0%';
                timerStatus.textContent = "专注进行中...";
                
                timer = setInterval(() => {
                    if (isPaused) return;
                    
                    remainingSeconds--;
                    
                    // 每分钟添加一个emoji
                    const currentMinutesPassed = Math.floor((totalSeconds - remainingSeconds) / 60);
                    if (currentMinutesPassed > minutesPassed) {
                        minutesPassed = currentMinutesPassed;
                        
                        if (minutesPassed <= totalMinutes) {
                            addEmojiToTrack(minutesPassed);
                            sound.play();
                            timerStatus.textContent = `已经过去 ${minutesPassed} 分钟了，${userReminder}`;
                        }
                    }
                    
                    updateTimerDisplay();
                    
                    // 更新进度条
                    const progress = ((totalSeconds - remainingSeconds) / totalSeconds) * 100;
                    progressBar.style.width = `${progress}%`;
                    
                    // 计时结束
                    if (remainingSeconds <= 0) {
                        clearInterval(timer);
                        isRunning = false;
                        startBtn.style.display = 'flex';
                        pauseBtn.style.display = 'none';
                        timerStatus.textContent = '计时结束！做得很好！';
                        endSound.play();
                        showBigEmoji();
                    }
                }, 1000);
            });
            
            // 生成随机emoji
            function generateRandomEmojis(total) {
                selectedEmojis = [];
                
                // 生成前n-1个普通emoji
                for (let i = 0; i < total - 1; i++) {
                    const randomIndex = Math.floor(Math.random() * normalEmojis.length);
                    selectedEmojis.push(normalEmojis[randomIndex]);
                }
                
                // 最后一个使用生物类emoji
                const randomLivingIndex = Math.floor(Math.random() * livingEmojis.length);
                lastEmoji = livingEmojis[randomLivingIndex];
                selectedEmojis.push(lastEmoji);
            }
            
            // 暂停计时
            pauseBtn.addEventListener('click', () => {
                isPaused = !isPaused;
                if (isPaused) {
                    pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
                    timerStatus.textContent = '已暂停';
                } else {
                    pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                    timerStatus.textContent = `已经过去 ${minutesPassed} 分钟了，${userReminder}`;
                }
            });
            
            // 添加emoji到轨道
            function addEmojiToTrack(minuteIndex) {
                if (minuteIndex > selectedEmojis.length) return;
                
                // 移除所有对话泡
                document.querySelectorAll('.emoji-container').forEach(container => {
                    container.classList.remove('active');
                });
                
                const emojiContainer = document.createElement('div');
                emojiContainer.className = 'emoji-container';
                
                const bubble = document.createElement('div');
                bubble.className = 'emoji-bubble';
                bubble.textContent = userReminder;
                
                const emoji = document.createElement('div');
                emoji.className = 'track-emoji';
                emoji.textContent = selectedEmojis[minuteIndex - 1];
                
                // 激活当前对话泡
                emojiContainer.classList.add('active');
                
                emojiContainer.appendChild(bubble);
                emojiContainer.appendChild(emoji);
                emojiTrack.appendChild(emojiContainer);
                
                // 自动滚动到底部
                emojiTrack.scrollTop = emojiTrack.scrollHeight;
            }
            
            // 更新计时器显示
            function updateTimerDisplay() {
                const mins = Math.floor(remainingSeconds / 60);
                const secs = remainingSeconds % 60;
                timerElement.textContent = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
            
            // 重置计时器
            resetBtn.addEventListener('click', () => {
                resetTimer();
            });
            
            // 关闭按钮（返回设置界面）
            closeBtn.addEventListener('click', () => {
                document.body.classList.remove('timer-active');
                closeBtn.style.display = 'none';
                resetTimer();
            });
            
            // 重置计时器函数
            function resetTimer() {
                clearInterval(timer);
                isRunning = false;
                isPaused = false;
                remainingSeconds = totalMinutes * 60;
                updateTimerDisplay();
                timerStatus.textContent = '设置专注时长并开始计时';
                progressBar.style.width = '0%';
                startBtn.style.display = 'flex';
                pauseBtn.style.display = 'none';
                pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
                
                // 重置轨道
                trackPlaceholder.style.display = 'block';
                emojiTrack.innerHTML = '<div id="track-placeholder">Emoji将在这里显示，每分钟出现一个</div>';
                
                // 隐藏大emoji
                bigEmoji.style.display = 'none';
            }
            
            // 显示大emoji动画
            function showBigEmoji() {
                if (!lastEmoji) return;
                
                bigEmoji.style.display = 'block';
                bigEmoji.textContent = lastEmoji;
                bigEmoji.classList.add('animate-emoji');
                
                // 添加点击事件
                bigEmoji.onclick = function() {
                    bigEmoji.classList.remove('animate-emoji');
                    bigEmoji.style.display = 'none';
                };
            }
        });
    </script>
</body>
</html>