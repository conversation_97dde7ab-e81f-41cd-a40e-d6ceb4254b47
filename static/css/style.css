:root {
    --primary-color: #4a6bff;
    --primary-hover: #3a5bef;
    --secondary-color: #f5f7ff;
    --danger-color: #ff4d4f;
    --danger-hover: #ff7875;
    --text-color: #333;
    --light-text: #666;
    --border-color: #e0e0e0;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --sidebar-width: 240px;
  }
  
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #f9fafc;
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
  }
  
  .app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
  }
  
  /* 侧边栏样式 */
  .sidebar {
    width: var(--sidebar-width);
    background-color: white;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .sidebar-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .db-selector {
    position: relative;
  }
  
  .current-db {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: var(--dynamic-bg-color);
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
  }
  
  .db-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: var(--dynamic-bg-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8);
    z-index: 100;
    display: none;
  }
  
  .db-dropdown.show {
    display: block;
  }
  
  .db-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .db-item:hover {
    background-color: var(--secondary-color);
  }
  
  .db-item.active {
    background-color: var(--primary-color);
    color: white;
  }
  
  .groups-container {
    background-color: var(--dynamic-bg-color);
    flex: 1;
    overflow-y: auto;
    padding: 15px;
  }
  
  .groups-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .groups-header h3 {
    font-size: 16px;
    font-weight: 600;
  }
  
  .add-group-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 16px;
  }
  
  .groups-list {
    list-style: none;
  }
  
  .group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .group-item:hover {
    background-color: var(--secondary-color);
  }
  
  .group-item.active {
    background-color: var(--primary-color);
    color: white;
  }
  
  .group-item.active .group-count {
    background-color: rgba(255, 255, 255, 0.3);
  }
  
  .group-count {
    background-color: var(--secondary-color);
    color: var(--light-text);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
  }
  
  .group-item.active .group-count {
    color: white;
  }
  
  /* 主内容区样式 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .main-content > .content-area,
  .main-content > .iframe-container {
    flex: 0 0 auto;
    margin-bottom: 10px;
    height: calc(100% - 90px);
  }
  
  .main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: white;
  }
  
  .search-container {
    display: flex;
    width: 40%;
  }
  
  .search-container input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    font-size: 14px;
  }
  
  .search-container button {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .search-container button:hover {
    background-color: var(--primary-hover);
  }
  
  .actions {
    display: flex;
    gap: 10px;
  }
  
  /* 按钮样式 */
  .btn {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
  }
  
  .btn.primary {
    background-color: var(--primary-color);
    color: white;
  }
  
  .btn.primary:hover {
    background-color: var(--primary-hover);
  }
  
  .btn.secondary {
    background-color: #f0f0f0;
    color: var(--text-color);
  }
  
  .btn.secondary:hover {
    background-color: #e0e0e0;
  }
  
  .btn.danger {
    background-color: var(--danger-color);
    color: white;
  }
  
  .btn.danger:hover {
    background-color: var(--danger-hover);
  }
  
  .btn.cancel {
    background-color: #f0f0f0;
    color: var(--text-color);
  }
  
  .btn.cancel:hover {
    background-color: #e0e0e0;
  }
  
  .btn.small {
    padding: 5px 10px;
    font-size: 12px;
  }
  
  /* 内容区域 */
  .content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    position: relative;
  }
  
  /* 网站卡片网格 */
  .websites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .website-card {
    
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
  }
  
  .website-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
  
  .website-card .card-img {
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    padding: 0px;
    cursor: pointer;
  }
  
  .website-card .card-img img {
    max-width: 100%;
    max-height: 100%;
    min-height: 70%;
    object-fit: contain;
  }
  
  .website-card .card-content {
    padding:0px;
  }
  
  .website-card .card-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .website-card .card-url {
    font-size: 12px;
    color: var(--light-text);
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .website-card .card-note {
    font-size: 13px;
    color: var(--text-color);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 10px;
  }
  
  .website-card .card-actions {
    display: flex;
    justify-content: space-between;
  }
  
  .website-card .card-actions a {
    text-decoration: none;
    color: var(--text-color);
    font-size: 13px;
    font-weight: 500;
  }
  
  .website-card .card-actions .delete-btn {
    color: var(--danger-color);
    cursor: pointer;
    display: none;
  }
  
  .website-card .card-actions a:hover,
  .website-card .card-actions .delete-btn:hover {
    text-decoration: underline;
  }
  
  /* 管理模式 */
  .manage-mode .website-card {
    cursor: pointer;
  }
  
  .manage-mode .website-card .card-actions .delete-btn {
    display: block;
  }
  
  .manage-mode .website-card.selected {
    border: 2px solid var(--primary-color);
  }
  
  .manage-mode .website-card.selected::before {
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .management-toolbar {
    position: fixed;
    bottom: 0;
    left: var(--sidebar-width);
    right: 0;
    background-color: white;
    border-top: 1px solid var(--border-color);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
  }
  
  /* 模态框样式 */
  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modal-content {
    background-color: white;
    margin: 50px auto;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    position: relative;
  }
  
  .close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
    color: var(--light-text);
  }
  
  .modal h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .form-group small {
    display: block;
    color: var(--light-text);
    margin-top: 5px;
    font-size: 12px;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
  }
  
  .form-group textarea {
    height: 80px;
    resize: vertical;
  }
  
  .select-container {
    display: flex;
    gap: 10px;
  }
  
  .select-container select {
    flex: 1;
  }
  
  .image-upload {
    display: flex;
    gap: 20px;
  }
  
  .preview-container {
    width: 100px;
    height: 100px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  .preview-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  
  .upload-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
  }
  
  .upload-actions input[type="file"] {
    display: none;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
  }
  
  /* 图片清理模态框 */
  .unused-images-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
  }
  
  .unused-image-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .unused-image-item:last-child {
    border-bottom: none;
  }
  
  .unused-image-item .image-checkbox {
    margin-right: 15px;
  }
  
  .unused-image-item .image-preview {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: var(--secondary-color);
    border-radius: 4px;
    overflow: hidden;
  }
  
  .unused-image-item .image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
  
  .unused-image-item .image-info {
    flex: 1;
  }
  
  .unused-image-item .image-path {
    font-size: 13px;
    color: var(--light-text);
  }
  
  /* 图片查看器 */
  .image-viewer-modal {
    background-color: rgba(0, 0, 0, 0.9);
  }
  
  .image-viewer-container {
    width: 90%;
    max-width: 1000px;
    height: 90%;
    margin: 2% auto;
    background-color: #222;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .image-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #333;
    color: white;
  }
  
  .image-viewer-header h3 {
    font-size: 18px;
    font-weight: 500;
  }
  
  .image-viewer-header .close {
    color: white;
    top: auto;
    right: auto;
    position: relative;
  }
  
  .image-viewer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .image-viewer-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
  }
  
  .image-container {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }
  
  #viewer-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }
  
  .nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
  }
  
  .prev-btn {
    left: 20px;
  }
  
  .next-btn {
    right: 20px;
  }
  
  .image-viewer-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 15px;
    background-color: #333;
  }
  
  .control-btn {
    background-color: #444;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .control-btn:hover {
    background-color: #555;
  }
  
  /* 加载动画 */
  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0;
    gap: 15px;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  /* 移动端菜单切换按钮 */
  .mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1000;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 18px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: var(--transition);
  }

  .mobile-menu-toggle:hover {
    background: var(--primary-hover);
  }

  /* 移动端侧边栏遮罩 */
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .websites-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 15px;
    }
  }

  @media (max-width: 768px) {
    .mobile-menu-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .app-container {
      position: relative;
    }

    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      width: var(--sidebar-width);
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      border-right: 1px solid var(--border-color);
      border-bottom: none;
    }

    .sidebar.mobile-open {
      transform: translateX(0);
    }

    .sidebar-overlay.show {
      display: block;
    }

    .main-content {
      width: 100%;
      margin-left: 0;
    }

    .main-header {
      flex-direction: column;
      align-items: stretch;
      gap: 15px;
      padding-left: 60px; /* 为菜单按钮留出空间 */
    }

    .search-container {
      width: 100%;
    }

    .websites-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;
      padding: 15px;
    }

    .website-card {
      padding: 12px;
    }

    /*.website-card img {
      width: 40px;
      height: 40px;
    }*/

    .website-card h3 {
      font-size: 14px;
      margin: 8px 0 4px 0;
    }

    .website-card p {
      font-size: 12px;
    }

    .image-upload {
      flex-direction: column;
      align-items: center;
    }

    .management-toolbar {
      left: 0;
      right: 0;
      padding: 10px 15px;
    }

    .image-viewer-container {
      width: 100%;
      height: 100%;
      margin: 0;
      border-radius: 0;
    }

    /* 模态框在移动端的调整 */
    .modal-content {
      width: 95%;
      margin: 20px auto;
      max-height: 90vh;
      overflow-y: auto;
    }

    .form-actions {
      flex-direction: column;
      gap: 10px;
    }

    .form-actions .btn {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .main-header {
      padding: 15px 10px 15px 50px;
    }

    .websites-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 10px;
      padding: 10px;
    }

    .website-card {
      padding: 8px;
    }

    /*.website-card img {
      width: 32px;
      height: 32px;
    }*/

    .website-card h3 {
      font-size: 12px;
      margin: 6px 0 2px 0;
    }

    .website-card p {
      font-size: 11px;
    }

    .actions {
      flex-direction: column;
      gap: 8px;
    }

    .actions .btn {
      width: 100%;
      font-size: 14px;
      padding: 8px 12px;
    }
  }
  
  /* 拖拽样式 */
  .dragging {
    opacity: 0.5;
  }
  
  .drag-over {
    background-color: var(--secondary-color);
    border: 2px dashed var(--primary-color);
  }
  
  .no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    color: var(--light-text);
  }
  
  /* iframe 控制容器 */
  .iframe-container {
    position: relative;
    margin-top: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
  }
  
  .iframe-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    display: flex;
    gap: 5px;
  }
  
  .iframe-controls button {
    background-color: rgba(255, 255, 255, 0.8);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 4px;
    transition: var(--transition);
  }
  
  .iframe-controls button:hover {
    background-color: var(--secondary-color);
  }

  /* 设置面板样式 */
  .settings-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
  }

  .settings-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
  }

  .settings-section h3 {
    margin: 0 0 16px 0;
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 600;
  }

  .form-group {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;
  }

  .form-group label {
    min-width: 120px;
    font-weight: 500;
    color: var(--text-color);
  }

  .form-group input[type="range"] {
    flex: 1;
    margin-right: 8px;
  }

  .form-group input[type="color"] {
    width: 60px;
    height: 36px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    padding: 0;
  }

  .form-group select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
  }

  .range-value {
    min-width: 50px;
    text-align: center;
    font-size: 14px;
    color: var(--light-text);
    background: var(--secondary-color);
    padding: 4px 8px;
    border-radius: 4px;
  }

  /* 动态样式变量 */
  :root {
    --dynamic-card-size: 200px;
    --dynamic-card-gap: 20px;
    --dynamic-group-height: 45px;
    --dynamic-bg-color: #f9fafc;
    --dynamic-card-bg-color: #ffffff;
    --dynamic-text-color: #333333;
    --dynamic-primary-color: #4a6bff;
  }

  /* 使用动态变量的样式 */
  body {
    background-color: var(--dynamic-bg-color);
    color: var(--dynamic-text-color);
  }

  .websites-grid {
    gap: var(--dynamic-card-gap);
  }

  .website-card {
    background: var(--dynamic-card-bg-color);
    color: var(--dynamic-text-color);}
  .website-card h3 {
    text-shadow: 1px 1px 6px #0000005c;
  }

  .group-item {
    height: var(--dynamic-group-height);
  }

  /* 卡片大小变体 */
  .card-size-small .websites-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .card-size-small .website-card {
    padding: 12px;
  }

  /*.card-size-small .website-card img {
    width: 32px;
    height: 32px;
  }*/

  .card-size-small .website-card h3 {
    font-size: 13px;
    margin: 6px 0 4px 0;
  }

  .card-size-small .website-card p {
    font-size: 11px;
  }

  .card-size-medium .websites-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .card-size-large .websites-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .card-size-large .website-card {
    padding: 10px;
  }
.website-card {
    padding: 10px;
  }
  /*.card-size-large .website-card img {
    width: 56px;
    height: 56px;
  }*/

  .card-size-large .website-card h3 {
    font-size: 16px;
    margin: 12px 0 6px 0;
  }

  .card-size-large .website-card p {
    font-size: 14px;
  }

.card-url a{
  color: var(--dynamic-primary-color)!important;
}
.group-item:hover{
  color: var(--dynamic-primary-color)!important;

}
.card-content a{
  color:var(--dynamic-primary-color);
}